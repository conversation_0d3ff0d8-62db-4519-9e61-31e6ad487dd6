/* Blog Post Styling */

/* Base styles */
body {
    font-family: 'Inter', sans-serif;
    background-color: #f8fafc; /* Lighter gray background */
    background-image: radial-gradient(#e2e8f0 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Prose styling for article content */
.prose {
    max-width: 65ch;
    margin-left: auto;
    margin-right: auto;
    color: #1f2937; /* text-gray-800 */
}

.prose h2 {
    font-size: 1.875rem; /* text-3xl */
    font-weight: 700; /* font-bold */
    margin-top: 2.5rem; /* mt-10 */
    margin-bottom: 1rem; /* mb-4 */
    color: #1a202c; /* text-gray-900 */
}

.prose h3 {
    font-size: 1.5rem; /* text-2xl */
    font-weight: 600; /* font-semibold */
    margin-top: 2rem; /* mt-8 */
    margin-bottom: 0.75rem; /* mb-3 */
    color: #1a202c; /* text-gray-900 */
}

.prose p {
    font-size: 1.125rem; /* text-lg */
    line-height: 1.75; /* leading-relaxed */
    margin-bottom: 1.25rem; /* mb-5 */
    color: #4a5568; /* text-gray-700 */
}

.prose ul {
    list-style: none; /* Remove default bullet */
    padding-left: 0;
    margin-bottom: 1.5rem; /* mb-6 */
}

.prose ul li {
    position: relative;
    padding-left: 1.75rem; /* Space for custom bullet */
    margin-bottom: 0.75rem; /* mb-3 */
    color: #4a5568; /* text-gray-700 */
}

.prose ul li::before {
    content: "\2022"; /* Unicode for a solid bullet */
    color: #3b82f6; /* Blue bullet */
    font-size: 1.25rem; /* Larger bullet */
    position: absolute;
    left: 0;
    top: 0.125rem; /* Adjust vertical alignment */
}

/* Header styles */
.blog-header {
    background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
    color: white;
    padding: 4rem 1rem;
    margin-bottom: 2rem;
    border-radius: 0 0 1.5rem 1.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.blog-header h1 {
    font-size: 2.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1rem;
}

/* Article card styles */
.article-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid #e5e7eb;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .prose h2 {
        font-size: 1.5rem;
    }
    
    .prose h3 {
        font-size: 1.25rem;
    }
    
    .prose p, .prose li {
        font-size: 1rem;
    }
    
    .blog-header h1 {
        font-size: 2rem;
    }
}
