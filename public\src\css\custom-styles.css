/* Global Styles */
body {
    font-family: 'Inter', sans-serif;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Back to Top Button Styles */
.go-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: rgba(37, 99, 235, 0.9);
    color: white;
    width: 3rem;
    height: 3rem;
    border-radius: 9999px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.1);
    z-index: 50;
    border: none;
}

.go-to-top:hover {
    background: rgba(29, 78, 216, 0.95);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(37, 99, 235, 0.2);
}

.go-to-top.visible {
    opacity: 1;
    visibility: visible;
}

/* Gradient Backgrounds */
.header-gradient {
    background: linear-gradient(135deg, #e0f2fe, #dbeafe);
}

.content-block-bg {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* Text Gradient Effects */
.section-header-gradient {
    background: linear-gradient(45deg, #3b82f6, #6366f1);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

.section-header-blue {
    background-image: linear-gradient(to right, #60a5fa, #3b82f6, #2563eb);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.section-header-purple {
    background-image: linear-gradient(to right, #a78bfa, #8b5cf6, #7c3aed);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

/* Table Styling */
.impact-matrix table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1.5rem;
    background-color: #fff;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

.impact-matrix th,
.impact-matrix td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

.impact-matrix th {
    background-color: #f8fafc;
    font-weight: 600;
    color: #475569;
}

.impact-matrix tbody tr:last-child td {
    border-bottom: none;
}

.impact-matrix tbody tr:nth-child(even) {
    background-color: #f8fafc;
}
