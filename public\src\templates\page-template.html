<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Page description for SEO">
    <title>Page Title - RebootingwithAI</title>
    
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="../css/custom-styles.css">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        
        /* Add any page-specific styles here */
    </style>
</head>
<body class="bg-gray-50 text-gray-800">
    
    
    <!-- Main Content -->
    <main class="min-h-screen">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Page Header -->
            <header class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                    Page Title
                </h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Page description or subtitle goes here
                </p>
            </header>
            
            <!-- Page Content -->
            <section class="prose prose-lg max-w-none">
                <!-- Add your page content here -->
                <p>This is a template for creating new pages with consistent header and footer.</p>
                
                <h2>Features</h2>
                <ul>
                    <li>Automatic header and footer insertion</li>
                    <li>Responsive design with Tailwind CSS</li>
                    <li>Font Awesome icons</li>
                    <li>Inter font family</li>
                    <li>SEO-friendly structure</li>
                </ul>
                
                <h2>Usage</h2>
                <p>Copy this template and modify the content as needed. The header and footer will be automatically inserted by the common-components.js script.</p>
            </section>
        </div>
    </main>
    
    
    <div id="footer-placeholder"></div>
    
    <!-- Back to Top Button -->
    <button class="go-to-top" onclick="scrollToTop()" aria-label="Back to top">
        <i class="fas fa-arrow-up"></i>
    </button>
    
    <!-- Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-analytics-compat.js"></script>
    <script type="module" src="../js/common-components.js"></script>
    <script type="module" src="../js/app.js"></script>
</body>
</html>
