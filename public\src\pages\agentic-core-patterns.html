<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agentic Core Patterns</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/custom-styles.css">
</head>
<body class="bg-gray-50 text-gray-800">
    <div id="header-placeholder"></div>
    <div class="max-w-6xl mx-auto px-4 py-8">
        <!-- Navigation -->
        <nav class="flex flex-col md:flex-row justify-between items-center mb-10 gap-3 w-full">
            <div class="flex flex-1 justify-start w-full md:w-auto gap-3">
                <a href="agentic-design-pattern.html" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold py-2 px-4 rounded-lg bg-blue-50 hover:bg-blue-100 transition duration-300 shadow-sm border border-blue-200 text-base">
                    <i class="fas fa-arrow-left mr-2"></i> Previous
                </a>
            </div>
            <div class="flex flex-1 justify-center w-full md:w-auto gap-3">
                <a href="../../learning-hub.html" class="inline-flex items-center text-gray-700 hover:text-blue-700 font-semibold py-2 px-4 rounded-lg bg-gray-100 hover:bg-gray-200 transition duration-300 shadow-sm border border-gray-300 text-base">
                    <i class="fas fa-home mr-2"></i> Home
                </a>
            </div>
            <div class="flex flex-1 justify-end w-full md:w-auto gap-3">
                <a href="agentic-workflow-patterns.html" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold py-2 px-4 rounded-lg bg-blue-50 hover:bg-blue-100 transition duration-300 shadow-sm border border-blue-200 text-base">
                    Next <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
        </nav>
        
        <header class="header-gradient rounded-3xl shadow-2xl p-12 mb-16 text-center border border-blue-300 transform transition-all duration-500 hover:scale-[1.01] hover:shadow-3xl">
            <h1 class="text-5xl md:text-6xl font-extrabold text-blue-900 mb-5 tracking-tight drop-shadow-lg leading-tight">Agentic Core Patterns</h1>
            <p class="text-2xl text-blue-700 font-medium opacity-90">Foundation patterns for building robust agentic systems</p>
        </header>

        <!-- Pattern Navigation Tiles -->
        <nav class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-8 mb-16">
            <a href="#prompt-chaining" class="bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-blue-200 hover:ring-blue-300 transform hover:scale-105">
                <div class="text-5xl text-blue-600 mb-3"><i class="fas fa-link"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-blue-800 text-center">Prompt Chaining</h3>
                <p class="text-gray-600 text-center text-sm">Sequential task decomposition for complex workflows</p>
            </a>
            <a href="#routing" class="bg-gradient-to-br from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-purple-200 hover:ring-purple-300 transform hover:scale-105">
                <div class="text-5xl text-purple-600 mb-3"><i class="fas fa-route"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-purple-800 text-center">Routing</h3>
                <p class="text-gray-600 text-center text-sm">Intelligent input direction to specialized sub-agents</p>
            </a>
            <a href="#parallelization" class="bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-green-200 hover:ring-green-300 transform hover:scale-105">
                <div class="text-5xl text-green-600 mb-3"><i class="fas fa-code-branch"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-green-800 text-center">Parallelization</h3>
                <p class="text-gray-600 text-center text-sm">Concurrent task execution for improved efficiency</p>
            </a>
            <a href="#reflection" class="bg-gradient-to-br from-yellow-50 to-yellow-100 hover:from-yellow-100 hover:to-yellow-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-yellow-200 hover:ring-yellow-300 transform hover:scale-105">
                <div class="text-5xl text-yellow-600 mb-3"><i class="fas fa-brain"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-yellow-800 text-center">Reflection</h3>
                <p class="text-gray-600 text-center text-sm">Self-evaluation and continuous improvement</p>
            </a>
            <a href="#planning" class="bg-gradient-to-br from-pink-50 to-pink-100 hover:from-pink-100 hover:to-pink-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-pink-200 hover:ring-pink-300 transform hover:scale-105">
                <div class="text-5xl text-pink-600 mb-3"><i class="fas fa-project-diagram"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-pink-800 text-center">Planning</h3>
                <p class="text-gray-600 text-center text-sm">Goal-oriented task structuring and sequencing</p>
            </a>
        </nav>

        <!-- Prompt Chaining Section -->
        <section id="prompt-chaining" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold section-header-gradient flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-blue-200">
                    <i class="fas fa-link text-blue-600"></i> Prompt Chaining
                </h2>
            </div>
            <div class="content-block-bg rounded-3xl shadow-xl ring-2 ring-blue-100 p-10 mb-10 border border-blue-200">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-blue-700">
                        <i class="fas fa-info-circle text-blue-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">Prompt Chaining is a pattern that breaks down complex tasks into a sequence of smaller, focused prompts. Each prompt in the chain builds upon the results of previous prompts, creating a structured flow of information and reasoning. This pattern is particularly useful for complex tasks that require multiple steps of processing or reasoning.</p>
                    <p class="text-gray-700 leading-relaxed mt-2">There are several approaches to implementing prompt chaining, each with its own trade-offs:</p>
                    <ul class="list-disc list-inside text-gray-700 ml-6 mt-4 space-y-2">
                        <li><span class="font-bold text-gray-800">Basic Python Functions:</span> The most straightforward approach, chaining functions that call an LLM API. This method is easy to understand and implement, but lacks the advanced features of specialized libraries, such as automatic optimization and state management.</li>
                        <li><span class="font-bold text-gray-800">DSPy:</span> A framework from Stanford that provides a more declarative way to define and optimize prompt chains. DSPy separates the logic of the program (the modules) from the parameters (the prompts and model configurations), which allows for automatic optimization of the prompts.</li>
                        <li><span class="font-bold text-gray-800">LangChain:</span> A popular library for building applications with LLMs. LangChain provides a comprehensive set of tools for building complex chains, including state management, memory, and integrations with other services.</li>
                    </ul>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-blue-700">
                        <i class="fas fa-code text-blue-500"></i> Basic Python Implementation
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code>from openai import OpenAI

# Initialize client
client = OpenAI()

def analyze_sales_call(transcript: str) -> dict:
    """Analyze a sales call transcript using prompt chaining."""
    # Step 1: Summarize the call
    summary = client.chat.completions.create(
        model="gpt-4-turbo-preview",
        messages=[{"role": "user", "content": f"Summarize this sales call:\n{transcript}"}]
    ).choices[0].message.content
    
    # Step 2: Extract key insights
    insights = client.chat.completions.create(
        model="gpt-4-turbo-preview",
        messages=[{"role": "user", "content": f"Extract key insights from this summary:\n{summary}"}]
    ).choices[0].message.content
    
    # Step 3: Generate next steps
    next_steps = client.chat.completions.create(
        model="gpt-4-turbo-preview",
        messages=[{"role": "user", "content": f"Based on these insights, suggest next steps:\n{insights}"}]
    ).choices[0].message.content
    
    return {
        "summary": summary,
        "insights": insights,
        "next_steps": next_steps
    }

# Example usage
if __name__ == "__main__":
    # This would typically come from an audio file
    sample_transcript = """
    [Sales Rep] Thanks for joining us today. How can we help you?
    [Customer] We're looking to improve our team's productivity...
    """
    
    result = analyze_sales_call(sample_transcript)
    print(f"Summary: {result['summary']}")
    print(f"\nKey Insights: {result['insights']}")
    print(f"\nNext Steps: {result['next_steps']}")</code></pre>
                </div>

            </div>
        </section>

        <!-- Routing Section -->
        <section id="routing" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold section-header-gradient flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-purple-200">
                    <i class="fas fa-route text-purple-600"></i> Routing
                </h2>
            </div>
            <div class="content-block-bg rounded-3xl shadow-xl ring-2 ring-purple-100 p-10 mb-10 border border-purple-200">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-purple-700">
                        <i class="fas fa-info-circle text-purple-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">Routing is a pattern that directs inputs to different specialized agents or chains based on their content or purpose. It's like a traffic controller for AI agents, ensuring each query reaches the most appropriate handler. This pattern is particularly useful when you have multiple specialized agents and need to determine which one should handle a particular request.</p>
                    <p class="text-gray-700 leading-relaxed mt-2">There are several approaches to implementing routing:</p>
                    <ul class="list-disc list-inside text-gray-700 ml-6 mt-4 space-y-2">
                        <li><span class="font-bold text-gray-800">Rule-based Routing:</span> Uses predefined rules or conditions to direct inputs to specific handlers.</li>
                        <li><span class="font-bold text-gray-800">LLM-based Classification:</span> Uses a language model to analyze the input and determine the appropriate handler.</li>
                        <li><span class="font-bold text-gray-800">OpenAI Function Calling:</span> While primarily for tool use, the underlying mechanism of choosing which function to call based on a prompt is a form of routing. <a href="https://cookbook.openai.com/examples/how_to_call_functions_with_chat_models" target="_blank" class="underline text-purple-600 hover:text-purple-800 transition-colors duration-200">Read about OpenAI Function Calling</a>.</li>
                    </ul>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-purple-700">
                        <i class="fas fa-code text-purple-500"></i> Implementation using LangGraph
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code>from typing import Dict, List, Any, TypedDict, Annotated
# 1. Define agent functions
def support_agent(state):
    return {"messages": [{"role": "assistant", "content": "I'll help with your support request."}]}

def order_agent(state):
    return {"messages": [{"role": "assistant", "content": "Let me check your order status..."}]}

def product_agent(state):
    return {"messages": [{"role": "assistant", "content": "Here's information about the product..."}]}

# 2. Router function
def router(state):
    """Routes messages to the appropriate agent"""
    last_message = state["messages"][-1]["content"]
    if "order" in last_message.lower():
        return "order_agent"
    elif "product" in last_message.lower():
        return "product_agent"
    return "support_agent"

# 3. Example usage
app = create_workflow()
result = app.invoke({
    "messages": [{"role": "user", "content": "What's my order status?"}],
    "next": ""
})</code></pre>
                </div>

            </div>
        </section>

        <!-- Parallelization Section -->
        <section id="parallelization" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold section-header-gradient flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-green-200">
                    <i class="fas fa-code-branch text-green-600"></i> Parallelization
                </h2>
            </div>
            <div class="content-block-bg rounded-3xl shadow-xl ring-2 ring-green-100 p-10 mb-10 border border-green-200">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-green-700">
                        <i class="fas fa-info-circle text-green-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">Parallelization is a pattern that enables concurrent execution of multiple tasks or agents, significantly improving performance and throughput. This pattern is particularly useful for tasks that can be executed independently or when dealing with multiple data sources or processing streams.</p>
                    <p class="text-gray-700 leading-relaxed mt-2">There are several approaches to implementing parallelization:</p>
                    <ul class="list-disc list-inside text-gray-700 ml-6 mt-4 space-y-2">
                        <li><span class="font-bold text-gray-800">LangChain's RunnableParallel:</span> Simplifies running multiple chains in parallel and combining their outputs, ideal for independent AI operations. <a href="https://python.langchain.com/docs/how_to/parallel" target="_blank" rel="noopener noreferrer" class="underline text-green-600 hover:text-green-800 transition-colors duration-200">Read about RunnableParallel</a></li>
                        <li><span class="font-bold text-gray-800">Async/Await:</span> Python's asyncio for concurrent execution of I/O-bound tasks. <a href="https://docs.python.org/3/library/asyncio.html" target="_blank" class="underline text-green-600 hover:text-green-800 transition-colors duration-200">Python asyncio docs</a></li>
                        <li><span class="font-bold text-gray-800">Threading:</span> For CPU-bound tasks that can benefit from parallel execution. <a href="https://docs.python.org/3/library/threading.html" target="_blank" class="underline text-green-600 hover:text-green-800 transition-colors duration-200">Python threading docs</a></li>
                    </ul>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-green-700">
                        <i class="fas fa-code text-green-500"></i> Implementation using LangChain's RunnableParallel
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code># 1. Define processing chains
summary_chain = ChatPromptTemplate.from_template(
    "Summarize this: {text}"
) | model

sentiment_chain = ChatPromptTemplate.from_template(
    "Analyze sentiment: {text}"
) | model

# 2. Run in parallel
parallel_chain = RunnableParallel(
    summary=summary_chain,
    sentiment=sentiment_chain
)

# 3. Execute with input
results = parallel_chain.invoke({"text": "Sample text"})</code></pre>
                </div>

            </div>
        </section>

        <!-- Reflection Section -->
        <section id="reflection" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold section-header-gradient flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-yellow-200">
                    <i class="fas fa-brain text-yellow-600"></i> Reflection
                </h2>
            </div>
            <div class="content-block-bg rounded-3xl shadow-xl ring-2 ring-yellow-100 p-10 mb-10 border border-yellow-200">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-yellow-700">
                        <i class="fas fa-info-circle text-yellow-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">Reflection is a pattern that enables AI agents to evaluate their own performance, learn from past experiences, and improve their future actions. This pattern is crucial for building self-improving systems that can adapt to new situations and optimize their behavior over time.</p>
                    <p class="text-gray-700 leading-relaxed mt-2">There are several approaches to implementing reflection:</p>
                    <ul class="list-disc list-inside text-gray-700 ml-6 mt-4 space-y-2">
                        <li><span class="font-bold text-gray-800">Self-Evaluation:</span> Agents analyze their own outputs and decisions for quality and correctness.</li>
                        <li><span class="font-bold text-gray-800">Feedback Integration:</span> Incorporate external feedback to improve future responses.</li>
                        <li><span class="font-bold text-gray-800">Memory-Based Learning:</span> Store and learn from past interactions and outcomes.</li>
                        <li><span class="font-bold text-gray-800">Meta-Cognitive Analysis:</span> Evaluate the reasoning process and decision-making strategies.</li>
                    </ul>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-yellow-700">
                        <i class="fas fa-code text-yellow-500"></i> Implementation Pseudocode
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700 mb-6"><code># Pseudocode for a reflection loop
response = generate_answer(user_query)

for _ in range(max_reflections):
    critique = reflect_on(response)  # could be same or different LLM
    if critique.suggests_improvement:
        response = improve_answer(response, critique)
    else:
        break  # No more improvements, or max attempts reached

return response</code></pre>

                    <h4 class="font-bold text-xl mb-3 mt-6 text-yellow-600">Memory-Based Learning Example</h4>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code># Pseudocode for Memory System with Reflection

# 1. Basic Memory Storage
memory = [
    {
        'input': "How to reset password?",
        'output': "Click 'Forgot Password'",
        'feedback': "Worked!"
    },
    {
        'input': "Help me login",
        'output': "Enter username/password",
        'feedback': "Too vague"
    }
]

def find_similar(query):
    """Find most relevant memory for the query"""
    # In practice, use vector similarity or LLM
    return memory[0]  # Simplified for example

def reflect(query):
    """Analyze past interactions to improve responses"""
    similar = find_similar(query)
    return f"""
    Based on similar past interaction:
    Input: {similar['input']}
    Response: {similar['output']}
    Feedback: {similar['feedback']}
    
    Suggestion: Be more specific in responses
    """

# Example Usage
user_query = "I forgot my password"
print("Similar response:", find_similar(user_query)['output'])
print("\nReflection:", reflect(user_query))</code></pre>
                </div>

            </div>
        </section>

        <!-- Planning Section -->
        <section id="planning" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold section-header-gradient flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-pink-200">
                    <i class="fas fa-project-diagram text-pink-600"></i> Planning
                </h2>
            </div>
            <div class="content-block-bg rounded-3xl shadow-xl ring-2 ring-pink-100 p-10 mb-10 border border-pink-200">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-pink-700">
                        <i class="fas fa-info-circle text-pink-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">Planning is a pattern that enables AI agents to create and execute sequences of actions to achieve specific goals. By breaking down complex tasks into manageable steps and considering dependencies and constraints, this pattern enables systematic problem-solving and goal achievement.</p>
                    <p class="text-gray-700 leading-relaxed mt-2">There are several approaches to implementing planning systems:</p>
                    <ul class="list-disc list-inside text-gray-700 ml-6 mt-4 space-y-2">
                        <li><span class="font-bold text-gray-800">Hierarchical Planning:</span> Breaking down goals into sub-goals and creating hierarchical action plans.</li>
                        <li><span class="font-bold text-gray-800">Reactive Planning:</span> Adapting plans based on real-time feedback and changing conditions.</li>
                        <li><span class="font-bold text-gray-800">Constraint-Based Planning:</span> Considering various constraints and dependencies while creating plans.</li>
                        <li><span class="font-bold text-gray-800">Goal-Oriented Planning:</span> Focusing on achieving specific goals through systematic action sequences.</li>
                    </ul>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-pink-700">
                        <i class="fas fa-code text-pink-500"></i> Basic Planning Pseudocode
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code># Simple LLM client mock
llm = LLM()

# Define actions: what they need and what they produce
actions = [
    {
        'name': 'research_market',
        'needs': [],
        'gives': ['market_knowledge'],
        'description': 'Research market conditions and opportunities'
    },
    {
        'name': 'develop_strategy',
        'needs': ['market_knowledge'],
        'gives': ['business_strategy'],
        'description': 'Develop business strategy based on market research'
    }
]

def get_action(name):
    """Helper to get action by name"""
    return next((a for a in actions if a['name'] == name), None)

def suggest_next_action(state, goal_conditions):
    """Use LLM to suggest the next best action"""
    prompt = f"""Current state: {state}
    Goal conditions: {goal_conditions}
    Available actions: {[a['name'] for a in actions]}
    Suggest the most appropriate next action:"""
    
    action_name = llm.generate(f"suggest_action:{prompt}")
    return get_action(action_name)

def plan_actions(goal_conditions, actions, initial_state):
    plan = []
    state = set(initial_state)
    
    # Let LLM analyze the goal first
    analysis = llm.generate(f"analyze_state:Goal is {goal_conditions}. Current state is {state}")
    print(f"LLM Analysis: {analysis}")

    while not all(cond in state for cond in goal_conditions):
        # Get LLM suggestion for next action
        suggested_action = suggest_next_action(state, goal_conditions)
        if not suggested_action:
            print("No valid action found")
            break
            
        # Check prerequisites
        prereqs_met = all(p in state for p in suggested_action['needs'])
        
        if prereqs_met:
            plan.append(suggested_action['name'])
            state.update(suggested_action['gives'])
            print(f"âœ… Added action: {suggested_action['name']}")
        else:
            # If prerequisites not met, plan those first
            print(f"ðŸ” Planning prerequisites for: {suggested_action['name']}")
            for prereq in suggested_action['needs']:
                if prereq not in state:
                    # Find action that provides this prerequisite
                    for action in actions:
                        if prereq in action['gives']:
                            subplan = plan_actions([prereq], actions, state)
                            plan.extend(subplan)
                            state.update(prereq for a in subplan for prereq in get_action(a)['gives'] if get_action(a))
    
    return plan

# Example usage
initial_state = []
goal_conditions = ['business_strategy']

print("Starting planning process...")
plan = plan_actions(goal_conditions, actions, initial_state)
print("\nFinal Plan:", plan)</code></pre>
                </div>

            </div>
        </section>
    </div>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-analytics-compat.js"></script>
    <div id="footer-placeholder"></div>
    <script type="module" src="../js/common-components.js"></script>
    <script type="module" src="../js/app.js"></script>
</body>
</html>