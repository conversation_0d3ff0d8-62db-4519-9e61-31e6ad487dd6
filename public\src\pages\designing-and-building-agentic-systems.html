<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Designing and Building Agentic Systems - Agentic AI & Enterprise Architecture</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/custom-styles.css">
</head>
<body class="bg-gray-50 text-gray-800">
    <div id="header-placeholder"></div>
    <div class="max-w-6xl mx-auto px-4 py-8">
        <!-- Navigation -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-10 gap-3">
            <a href="foundational-understanding.html" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold py-2 px-4 rounded-lg bg-blue-50 hover:bg-blue-100 transition duration-300 shadow-sm border border-blue-200">
                <i class="fas fa-arrow-left mr-2"></i> Previous: Foundational
            </a>
            <a href="../../learning-hub.html" class="inline-flex items-center text-gray-700 hover:text-blue-700 font-semibold py-2 px-4 rounded-lg bg-gray-100 hover:bg-gray-200 transition duration-300 shadow-sm border border-gray-300">
                <i class="fas fa-home mr-2"></i> Home
            </a>
            <a href="enterprise-adoption-and-implementation.html" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold py-2 px-4 rounded-lg bg-blue-50 hover:bg-blue-100 transition duration-300 shadow-sm border border-blue-200">
                Next: Enterprise Adoption <i class="fas fa-arrow-right ml-2"></i>
            </a>
        </div>

        <header class="header-gradient rounded-3xl shadow-2xl p-12 mb-16 text-center border border-blue-300 transform transition-all duration-500 hover:scale-[1.01] hover:shadow-3xl">
            <h1 class="text-5xl md:text-6xl font-extrabold text-blue-900 mb-5 tracking-tight drop-shadow-lg leading-tight">Designing and Building Agentic Systems</h1>
            <p class="text-2xl text-blue-700 font-medium opacity-90">Comprehensive guide to designing, orchestrating, and implementing intelligent agentic systems</p>
        </header>

        <div class="content-block-bg rounded-2xl shadow-xl ring-2 ring-blue-100 p-8 mb-10 border border-blue-200 backdrop-blur-sm">
            <p class="text-gray-700 leading-relaxed">This section covers the practical aspects of designing and building agentic systems. Learn core design patterns, understand how to orchestrate multiple agents, and explore the relationships between different patterns to create intelligent, scalable systems.</p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <a href="agentic-design-pattern.html" class="bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center text-center shadow-lg ring-1 ring-blue-200 hover:ring-blue-300 transform hover:scale-105">
                <div class="text-5xl text-blue-600 mb-3"><i class="fas fa-magic"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-blue-800">Agentic Design Patterns</h3>
                <p class="text-gray-600 text-sm mb-4">Comprehensive collection of design patterns for creating robust, scalable, and intelligent agent systems</p>
                <div class="flex justify-between items-center w-full text-xs text-gray-500 mt-auto">
                    <span><i class="fas fa-chart-simple mr-1"></i> Intermediate</span>
                    <span><i class="fas fa-clock mr-1"></i> 2 hours total</span>
                </div>
            </a>

            <a href="comparative-analysis-and-pattern-interrelationships.html" class="bg-gradient-to-br from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center text-center shadow-lg ring-1 ring-purple-200 hover:ring-purple-300 transform hover:scale-105">
                <div class="text-5xl text-purple-600 mb-3"><i class="fas fa-sitemap"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-purple-800">Comparative Analysis & Pattern Relationships</h3>
                <p class="text-gray-600 text-sm mb-4">Understanding how different patterns compare, contrast, and work together in agentic systems</p>
                <div class="flex justify-between items-center w-full text-xs text-gray-500 mt-auto">
                    <span><i class="fas fa-chart-line mr-1"></i> Advanced</span>
                    <span><i class="fas fa-book-reader mr-1"></i> 30 min read</span>
                </div>
            </a>

            <a href="orchestrating-agentic-systems.html" class="bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center text-center shadow-lg ring-1 ring-green-200 hover:ring-green-300 transform hover:scale-105">
                <div class="text-5xl text-green-600 mb-3"><i class="fas fa-sliders-h"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-green-800">Orchestrating Agentic Systems</h3>
                <p class="text-gray-600 text-sm mb-4">Strategies for coordinating multiple agentic patterns and managing complex workflows</p>
                <div class="flex justify-between items-center w-full text-xs text-gray-500 mt-auto">
                    <span><i class="fas fa-chart-line mr-1"></i> Advanced</span>
                    <span><i class="fas fa-book-reader mr-1"></i> 45 min read</span>
                </div>
            </a>
        </div>
    </div>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-analytics-compat.js"></script>
    <div id="footer-placeholder"></div>
    <script type="module" src="../js/common-components.js"></script>
    <script type="module" src="../js/app.js"></script>
</body>
</html>
