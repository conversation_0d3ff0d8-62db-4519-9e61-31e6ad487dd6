<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agentic Learning Patterns</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/custom-styles.css">
</head>
<body class="bg-gray-50 text-gray-800">
    <div id="header-placeholder"></div>
    <div class="max-w-6xl mx-auto px-4 py-8">
        <!-- Navigation -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-10 gap-3">
            <a href="agentic-decision-patterns.html" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold py-2 px-4 rounded-lg bg-blue-50 hover:bg-blue-100 transition duration-300 shadow-sm border border-blue-200">
                <i class="fas fa-arrow-left mr-2"></i> Previous: Decision
            </a>
            <a href="../../learning-hub.html" class="inline-flex items-center text-gray-700 hover:text-blue-700 font-semibold py-2 px-4 rounded-lg bg-gray-100 hover:bg-gray-200 transition duration-300 shadow-sm border border-gray-300">
                <i class="fas fa-home mr-2"></i> Home
            </a>
            <a href="agentic-design-pattern.html" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold py-2 px-4 rounded-lg bg-blue-50 hover:bg-blue-100 transition duration-300 shadow-sm border border-blue-200">
                Back to Patterns <i class="fas fa-arrow-right ml-2"></i>
            </a>
        </div>
        
        <!-- Header -->
        <header class="header-gradient rounded-3xl shadow-2xl p-12 mb-16 text-center border border-blue-300 transform transition-all duration-500 hover:scale-[1.01] hover:shadow-3xl relative overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-100/60 to-indigo-100/40 pointer-events-none"></div>
            <div class="relative z-10">
                <h1 class="text-5xl md:text-6xl font-extrabold text-blue-900 mb-4 tracking-tight drop-shadow-lg leading-tight">
                    Agentic Learning Patterns
                </h1>
                <p class="text-2xl text-blue-700 font-medium opacity-90 mb-4">
                    Patterns for continuous learning and adaptation in agentic systems
                </p>
                <p class="max-w-3xl mx-auto text-lg text-blue-800/90 mb-2">
                    These patterns represent the cutting edge of learning mechanisms in modern agentic systems. While traditional machine learning patterns focus on model training and optimization, these patterns address how agents can learn and adapt in real-time, leveraging the unique capabilities of large language models and modern AI architectures. They are essential for creating truly intelligent and adaptive agentic systems.
                </p>
            </div>
        </header>

        <!-- Pattern Navigation Tiles -->
        <nav class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <a href="#in-context" class="bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-blue-200 hover:ring-blue-300 transform hover:scale-105">
                <div class="text-5xl text-blue-600 mb-3"><i class="fas fa-comments"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-blue-800 text-center">In-Context Learning</h3>
                <p class="text-gray-600 text-center text-sm">Learning from prompt examples</p>
            </a>

            <a href="#meta-learning" class="bg-gradient-to-br from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-purple-200 hover:ring-purple-300 transform hover:scale-105">
                <div class="text-5xl text-purple-600 mb-3"><i class="fas fa-brain"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-purple-800 text-center">Meta-Learning</h3>
                <p class="text-gray-600 text-center text-sm">Learning to learn</p>
            </a>

            <a href="#self-refinement" class="bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-green-200 hover:ring-green-300 transform hover:scale-105">
                <div class="text-5xl text-green-600 mb-3"><i class="fas fa-sync-alt"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-green-800 text-center">Self-Refinement</h3>
                <p class="text-gray-600 text-center text-sm">Learning from self-analysis</p>
            </a>

            <a href="#memory-augmented" class="bg-gradient-to-br from-yellow-50 to-yellow-100 hover:from-yellow-100 hover:to-yellow-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-yellow-200 hover:ring-yellow-300 transform hover:scale-105">
                <div class="text-5xl text-yellow-600 mb-3"><i class="fas fa-database"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-yellow-800 text-center">Memory-Augmented Learning</h3>
                <p class="text-gray-600 text-center text-sm">Learning from past experiences</p>
            </a>
        </nav>

        <!-- In-Context Learning Section -->
        <section id="in-context" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold bg-gradient-to-r from-blue-400 to-blue-700 bg-clip-text text-transparent flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-blue-200">
                    <i class="fas fa-comments text-blue-600"></i> In-Context Learning
                </h2>
            </div>
            <div class="bg-white/95 rounded-3xl shadow-xl ring-2 ring-blue-100 p-10 mb-10 border border-blue-200 backdrop-blur-sm">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-blue-700">
                        <i class="fas fa-info-circle text-blue-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">A learning pattern where the agent adapts its behavior based on examples provided in the current context or prompt, without requiring parameter updates. This pattern leverages the inherent capabilities of large language models to learn from examples in real-time, making it particularly valuable for rapid adaptation and task-specific learning.</p>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-blue-700">
                        <i class="fas fa-puzzle-piece text-blue-500"></i> Key Components
                    </h3>
                    <ul class="space-y-4 text-gray-700">
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Prompt Examples</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Carefully crafted examples that demonstrate the desired behavior</li>
                                <li>Can include both input-output pairs and step-by-step reasoning</li>
                                <li>Examples are provided in the context window of the model</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Zero-shot Learning</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Ability to perform tasks without explicit examples</li>
                                <li>Leverages model's pre-trained knowledge</li>
                                <li>Useful for well-defined, common tasks</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Few-shot Learning</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Learning from a small number of examples (typically 1-5)</li>
                                <li>Balances between zero-shot and many-shot approaches</li>
                                <li>Ideal for task-specific adaptation</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Chain-of-Thought</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Step-by-step reasoning process</li>
                                <li>Makes the learning process transparent</li>
                                <li>Enables complex problem-solving</li>
                            </ul>
                        </li>
                    </ul>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-blue-700">
                        <i class="fas fa-code text-blue-500"></i> Implementation # for In-Context Learning
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code>examples = [
    {"input": "Translate 'Hello' to Spanish.", "output": "Hola."},
    {"input": "What is 2 + 2?", "output": "4."}
]

def generate_prompt(task, examples):
    prompt = ""
    for ex in examples:
        prompt += f"Input: {ex['input']}\nOutput: {ex['output']}\n"
    prompt += f"Input: {task}\nOutput:"
    return prompt

user_task = "Translate 'Goodbye' to French."
prompt = generate_prompt(user_task, examples)
llm_response = llm_agent.complete(prompt)
print(llm_response)</code></pre>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-purple-700">
                        <i class="fas fa-code text-purple-500"></i> Implementation # for Meta-Learning
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code>class MetaLearner:
    def __init__(self):
        self.strategies = ["few-shot", "zero-shot"]
        self.history = []
    def select_strategy(self, task):
        # Analyze task and history to pick best strategy
        return "few-shot" if task.has_examples else "zero-shot"
    def learn(self, task):
        strategy = self.select_strategy(task)
        result = llm_agent.apply_strategy(strategy, task)
        self.history.append({"task": task, "strategy": strategy, "result": result})
        return result

task = Task(content="Translate 'Hello' to German.", has_examples=True)
meta_learner = MetaLearner()
result = meta_learner.learn(task)
print(result)
</code></pre>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-green-700">
                        <i class="fas fa-code text-green-500"></i> Implementation # for Self-Refinement
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code>def self_refine(task, max_iterations=3, threshold=0.8):
    output = llm_agent.complete(task)
    for i in range(max_iterations):
        critique = llm_agent.critique(task, output)
        if critique["score"] >= threshold:
            break
        output = llm_agent.improve(task, output, critique)
    return output

result = self_refine("Summarize the benefits of renewable energy.")
print(result)
</code></pre>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-yellow-700">
                        <i class="fas fa-code text-yellow-500"></i> Implementation # for Memory-Augmented Learning
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-yellow-700"><code>class Memory:
    def __init__(self):
        self.memories = []
    def store(self, experience):
        self.memories.append(experience)
    def retrieve(self, query):
        # Return most relevant memories (simple match for demo)
        return [m for m in self.memories if query in m["content"]]

memory = Memory()
memory.store({"content": "Agent completed task A."})
memory.store({"content": "User reported error on task B."})

query = "task A"
relevant = memory.retrieve(query)
print(relevant)
</code></pre>
                </div>
            </div>
        </section>

        <!-- Meta-Learning Section -->
        <section id="meta-learning" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold bg-gradient-to-r from-purple-400 to-purple-700 bg-clip-text text-transparent flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-purple-200">
                    <i class="fas fa-brain text-purple-600"></i> Meta-Learning
                </h2>
            </div>
            <div class="bg-white/95 rounded-3xl shadow-xl ring-2 ring-purple-100 p-10 mb-10 border border-purple-200 backdrop-blur-sm">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-purple-700">
                        <i class="fas fa-info-circle text-purple-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">A learning pattern where the agent develops the ability to quickly adapt to new tasks with minimal data by learning effective learning strategies. This pattern enables agents to become more efficient learners over time, reducing the need for extensive training data for new tasks.</p>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-purple-700">
                        <i class="fas fa-puzzle-piece text-purple-500"></i> Key Components
                    </h3>
                    <ul class="space-y-4 text-gray-700">
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Task Distribution</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Collection of diverse learning scenarios</li>
                                <li>Variety of task types and complexities</li>
                                <li>Balanced representation of different domains</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Learning Strategy</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Methods for adapting to new tasks</li>
                                <li>Optimization of learning parameters</li>
                                <li>Selection of appropriate learning approaches</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Quick Adaptation</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Rapid task mastery with minimal data</li>
                                <li>Efficient transfer of learned strategies</li>
                                <li>Optimization of learning speed</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Meta-parameters</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Learning rate adaptation</li>
                                <li>Strategy selection criteria</li>
                                <li>Performance optimization parameters</li>
                            </ul>
                        </li>
                    </ul>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-purple-700">
                        <i class="fas fa-code text-purple-500"></i> Implementation
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code># Python Pseudocode for Meta-Learning

class MetaLearner:
    def __init__(self):
        self.strategies = ["few-shot", "zero-shot"]
        self.history = []
    def select_strategy(self, task):
        # Analyze task and history to pick best strategy
        return "few-shot" if task.has_examples else "zero-shot"
    def learn(self, task):
        strategy = self.select_strategy(task)
        result = llm_agent.apply_strategy(strategy, task)
        self.history.append({"task": task, "strategy": strategy, "result": result})
        return result

task = Task(content="Translate 'Hello' to German.", has_examples=True)
meta_learner = MetaLearner()
result = meta_learner.learn(task)
print(result)
</code></pre>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-green-700">
                        <i class="fas fa-project-diagram text-green-500"></i> Related Patterns
                    </h3>
                    <ul class="list-disc list-inside ml-4 space-y-2 text-gray-700">
                        <li><strong>In-Context Learning:</strong> For example-based learning</li>
                        <li><strong>Self-Refinement:</strong> For performance improvement</li>
                        <li><strong>Memory-Augmented Learning:</strong> For experience storage</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Self-Refinement Section -->
        <section id="self-refinement" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold bg-gradient-to-r from-green-400 to-green-700 bg-clip-text text-transparent flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-green-200">
                    <i class="fas fa-sync-alt text-green-600"></i> Self-Refinement
                </h2>
            </div>
            <div class="bg-white/95 rounded-3xl shadow-xl ring-2 ring-green-100 p-10 mb-10 border border-green-200 backdrop-blur-sm">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-green-700">
                        <i class="fas fa-info-circle text-green-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">A learning pattern where the agent improves its performance by analyzing its own outputs and iteratively refining its approach through self-critique and adjustment. This pattern enables continuous improvement and quality enhancement without external supervision.</p>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-green-700">
                        <i class="fas fa-puzzle-piece text-green-500"></i> Key Components
                    </h3>
                    <ul class="space-y-4 text-gray-700">
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Self-Analysis</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Evaluation of output quality</li>
                                <li>Identification of improvement areas</li>
                                <li>Assessment of reasoning process</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Feedback Loop</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Iterative improvement process</li>
                                <li>Continuous quality enhancement</li>
                                <li>Adaptive refinement strategies</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Critique</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Performance assessment</li>
                                <li>Error identification</li>
                                <li>Quality metrics evaluation</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Adjustment</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Strategy modification</li>
                                <li>Parameter optimization</li>
                                <li>Behavior refinement</li>
                            </ul>
                        </li>
                    </ul>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-green-700">
                        <i class="fas fa-code text-green-500"></i> Implementation
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code># Python Pseudocode for Self-Refinement

def self_refine(task, max_iterations=3, threshold=0.8):
    output = llm_agent.complete(task)
    for i in range(max_iterations):
        critique = llm_agent.critique(task, output)
        if critique["score"] >= threshold:
            break
        output = llm_agent.improve(task, output, critique)
    return output

result = self_refine("Summarize the benefits of renewable energy.")
print(result)
</code></pre>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-green-700">
                        <i class="fas fa-project-diagram text-green-500"></i> Related Patterns
                    </h3>
                    <ul class="list-disc list-inside ml-4 space-y-2 text-gray-700">
                        <li><strong>In-Context Learning:</strong> For example-based learning</li>
                        <li><strong>Meta-Learning:</strong> For learning strategies</li>
                        <li><strong>Memory-Augmented Learning:</strong> For experience storage</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Memory-Augmented Learning Section -->
        <section id="memory-augmented" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold bg-gradient-to-r from-yellow-400 to-yellow-700 bg-clip-text text-transparent flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-yellow-200">
                    <i class="fas fa-database text-yellow-600"></i> Memory-Augmented Learning
                </h2>
            </div>
            <div class="bg-white/95 rounded-3xl shadow-xl ring-2 ring-yellow-100 p-10 mb-10 border border-yellow-200 backdrop-blur-sm">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-yellow-700">
                        <i class="fas fa-info-circle text-yellow-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">A learning pattern where the agent uses external memory systems to store and retrieve past experiences, enabling continual learning without requiring model retraining. This pattern is crucial for maintaining context and leveraging historical knowledge in agentic systems.</p>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-yellow-700">
                        <i class="fas fa-puzzle-piece text-yellow-500"></i> Key Components
                    </h3>
                    <ul class="space-y-4 text-gray-700">
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Vector Database</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Efficient storage of experience vectors</li>
                                <li>Semantic search capabilities</li>
                                <li>Scalable memory management</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Retrieval</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Context-aware memory access</li>
                                <li>Relevance-based retrieval</li>
                                <li>Dynamic memory selection</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Episodic Memory</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Event-based experience storage</li>
                                <li>Temporal sequence tracking</li>
                                <li>Context preservation</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Semantic Memory</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Knowledge-based storage</li>
                                <li>Concept organization</li>
                                <li>Relationship mapping</li>
                            </ul>
                        </li>
                    </ul>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-yellow-700">
                        <i class="fas fa-code text-yellow-500"></i> Implementation
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-yellow-700"><code># Python Pseudocode for Memory-Augmented Learning

class Memory:
    def __init__(self):
        self.memories = []
    def store(self, experience):
        self.memories.append(experience)
    def retrieve(self, query):
        # Return most relevant memories (simple match for demo)
        return [m for m in self.memories if query in m["content"]]

memory = Memory()
memory.store({"content": "Agent completed task A."})
memory.store({"content": "User reported error on task B."})

query = "task A"
relevant = memory.retrieve(query)
print(relevant)
</code></pre>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-yellow-700">
                        <i class="fas fa-project-diagram text-yellow-500"></i> Related Patterns
                    </h3>
                    <ul class="list-disc list-inside ml-4 space-y-2 text-gray-700">
                        <li><strong>In-Context Learning:</strong> For example-based learning</li>
                        <li><strong>Meta-Learning:</strong> For learning strategies</li>
                        <li><strong>Self-Refinement:</strong> For performance improvement</li>
                    </ul>
                </div>
            </div>
        </section>
    </div>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-analytics-compat.js"></script>
    <script type="module" src="../js/common-components.js"></script>
    <script type="module" src="../js/app.js"></script>
</body>
</html>
