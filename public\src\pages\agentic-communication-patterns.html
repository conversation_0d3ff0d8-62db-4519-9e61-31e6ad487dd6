<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agentic Communication Patterns</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/custom-styles.css">
</head>
<body class="bg-gray-50 text-gray-800 pattern-page">
    <div id="header-placeholder"></div>
    <div class="max-w-6xl mx-auto px-4 py-8">
        <!-- Navigation -->
        <nav class="flex flex-col md:flex-row justify-between items-center mb-10 gap-3 w-full">
            <div class="flex flex-1 justify-start w-full md:w-auto gap-3">
                <a href="agentic-workflow-patterns.html" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold py-2 px-4 rounded-lg bg-blue-50 hover:bg-blue-100 transition duration-300 shadow-sm border border-blue-200 text-base">
                    <i class="fas fa-arrow-left mr-2"></i> Previous: Workflow
                </a>
            </div>
            <div class="flex flex-1 justify-center w-full md:w-auto gap-3">
                <a href="../../learning-hub.html" class="inline-flex items-center text-gray-700 hover:text-blue-700 font-semibold py-2 px-4 rounded-lg bg-gray-100 hover:bg-gray-200 transition duration-300 shadow-sm border border-gray-300 text-base">
                    <i class="fas fa-home mr-2"></i> Home
                </a>
            </div>
            <div class="flex flex-1 justify-end w-full md:w-auto gap-3">
                <a href="agentic-decision-patterns.html" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold py-2 px-4 rounded-lg bg-blue-50 hover:bg-blue-100 transition duration-300 shadow-sm border border-blue-200 text-base">
                    Next: Decision <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
        </nav>
        
        <!-- Header -->
        <header class="header-gradient rounded-3xl shadow-2xl p-12 mb-16 text-center border border-blue-300 transform transition-all duration-500 hover:scale-[1.01] hover:shadow-3xl">
            <h1 class="text-5xl md:text-6xl font-extrabold text-blue-900 mb-5 tracking-tight drop-shadow-lg leading-tight">Agentic Communication Patterns</h1>
            <p class="text-2xl text-blue-700 font-medium opacity-90">Patterns for enabling effective communication and collaboration between agents</p>
        </header>

        <!-- Nav Tiles -->
        <nav class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <a href="#tool-calling" class="bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-blue-200 hover:ring-blue-300 transform hover:scale-105">
                <div class="text-5xl text-blue-600 mb-3"><i class="fas fa-tools"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-blue-800 text-center">Tool Calling</h3>
                <p class="text-gray-600 text-center text-sm">Function Execution</p>
            </a>
            <a href="#rag" class="bg-gradient-to-br from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-purple-200 hover:ring-purple-300 transform hover:scale-105">
                <div class="text-5xl text-purple-600 mb-3"><i class="fas fa-database"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-purple-800 text-center">RAG</h3>
                <p class="text-gray-600 text-center text-sm">Knowledge Retrieval</p>
            </a>
            <a href="#a2a" class="bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-green-200 hover:ring-green-300 transform hover:scale-105">
                <div class="text-5xl text-green-600 mb-3"><i class="fas fa-network-wired"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-green-800 text-center">Agent-to-Agent</h3>
                <p class="text-gray-600 text-center text-sm">Multi-Agent Systems</p>
            </a>
            <a href="#acp" class="bg-gradient-to-br from-yellow-50 to-yellow-100 hover:from-yellow-100 hover:to-yellow-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-yellow-200 hover:ring-yellow-300 transform hover:scale-105">
                <div class="text-5xl text-yellow-600 mb-3"><i class="fas fa-exchange-alt"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-yellow-800 text-center">Agent Communication</h3>
                <p class="text-gray-600 text-center text-sm">Structured Dialogue</p>
            </a>
        </nav>

        <!-- Tool Calling Section -->
        <section id="tool-calling" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold bg-gradient-to-r from-blue-400 to-blue-700 bg-clip-text text-transparent flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-blue-200">
                    <i class="fas fa-tools text-blue-600"></i> Tool Calling / Function Calling
                </h2>
            </div>
            <div class="content-block-bg rounded-3xl shadow-xl ring-2 ring-blue-100 p-10 mb-10 border border-blue-200">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-blue-700">
                        <i class="fas fa-info-circle text-blue-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">Want your AI agents to do more than just chat? Tool calling enables LLM agents to execute functions, from simple calculations to complex API calls. The Model Context Protocol (MCP) is a lightweight, flexible bridge that makes this possible, simplifying tool integration and letting your agents interact with APIs, databases, or scripts without complex setup. With just a few lines of code, your AI can access live data or trigger real-world actions.</p>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-blue-700">
                        <i class="fas fa-puzzle-piece text-blue-500"></i> Key Communication Protocols
                    </h3>
                    <ul class="space-y-4 text-gray-700">
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Function Registration</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Tools are registered with metadata describing their purpose, parameters, and return types.</li>
                                <li>The @mcp.tool() decorator makes functions agent-compatible with structured input validation.</li>
                                <li>Frameworks provide standardized ways to define and expose tool capabilities.</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Structured Invocation</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Agents generate structured requests (typically JSON) specifying the tool and arguments.</li>
                                <li>Includes parameter validation and type checking before execution.</li>
                                <li>Pydantic ensures structured input validation for robust tool interactions.</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Execution & Response</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Tools execute in a controlled environment with proper error handling.</li>
                                <li>Results are returned in a structured format for agent interpretation.</li>
                                <li>MCP servers can run locally or be exposed via ngrok for remote access.</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Security & Validation</h4>
                            <ul class="list-disc list-inside ml-4 space-y-1">
                                <li>Tools run with appropriate permissions and resource limits.</li>
                                <li>Input validation and sanitization before execution.</li>
                                <li>Authentication, authorization, and sandboxing for secure tool execution.</li>
                            </ul>
                        </li>
                    </ul>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-blue-700">
                        <i class="fas fa-code text-blue-500"></i> Step-by-Step: Building a Currency Converter Tool
                    </h3>
                    <div class="space-y-6">
                        <div>
                            <h4 class="font-semibold text-lg text-gray-800 mb-3">1. Set Up Your Environment</h4>
                            <p class="text-gray-700 mb-3">First, create a Python virtual environment and install dependencies:</p>
                            <pre class="bg-gray-900 text-gray-100 rounded-xl p-4 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code>pip install fastmcp pydantic crewai python-dotenv</code></pre>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-lg text-gray-800 mb-3">2. Define Your MCP Tool</h4>
                            <p class="text-gray-700 mb-3">Create a Python file (e.g., currency_tool.py) for your currency converter. This example uses a mock conversion rate, but you can swap in a real API for live data:</p>
                            <pre class="bg-gray-900 text-gray-100 rounded-xl p-4 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code>@mcp.tool()
async def convert_currency(request: CurrencyConversionInput) -> str:</code></pre>
                            <p class="text-gray-700 mt-2">The @mcp.tool() decorator makes this function agent-compatible, and pydantic ensures structured input validation.</p>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-lg text-gray-800 mb-3">3. Run the MCP Server</h4>
                            <p class="text-gray-700 mb-3">Start your server locally:</p>
                            <pre class="bg-gray-900 text-gray-100 rounded-xl p-4 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code>python currency_tool.py</code></pre>
                            <p class="text-gray-700 mt-2">This launches an MCP server at http://localhost:8000. Your tool is now ready to accept requests!</p>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-lg text-gray-800 mb-3">4. Expose Your Server with ngrok</h4>
                            <p class="text-gray-700 mb-3">To let agents access your tool, we need to expose your local server using ngrok:</p>
                            <ul class="list-disc list-inside ml-4 space-y-1 text-gray-700">
                                <li>Install ngrok and authenticate it.</li>
                                <li>Run: <code class="bg-gray-100 px-1 rounded">ngrok http 8000</code></li>
                                <li>Copy the generated URL (e.g., https://abc123.ngrok-free.app).</li>
                            </ul>
                            <p class="text-gray-700 mt-2 text-sm italic">Tip: ngrok's free tier is great for testing, but for production, consider a paid plan or deploy your server to a cloud provider.</p>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-lg text-gray-800 mb-3">5. Connect to CrewAI</h4>
                            <p class="text-gray-700 mb-3">Integrate your tool with a CrewAI agent:</p>
                            <pre class="bg-gray-900 text-gray-100 rounded-xl p-4 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code>server_config = "https://abc123.ngrok-free.app"  # use ngrok url

with MCPServerAdapter(server_config) as currency_tools:
    currency_agent = Agent(
        role="Currency Exchange Expert",
        goal="Help with currency conversions and exchange rate information",
        backstory="You're a knowledgeable financial assistant with access to real-time currency conversion tools.",
        tools=currency_tools,  # <- Pass the list of actual tool instances
        verbose=False
    )</code></pre>
                            <p class="text-gray-700 mt-2">Your agent can now call convert_currency seamlessly!</p>
                        </div>
                    </div>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-blue-700">
                        <i class="fas fa-exchange-alt text-blue-500"></i> stdio vs. SSE: Which to Use?
                    </h3>
                    <p class="text-gray-700 mb-4">MCP supports two communication modes for your server:</p>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="bg-blue-50 rounded-xl p-6 border border-blue-200">
                            <h4 class="font-semibold text-blue-800 mb-2 flex items-center">
                                <i class="fas fa-terminal text-blue-600 mr-2"></i>
                                stdio
                            </h4>
                            <p class="text-blue-700 text-sm">Ideal for local development or CLI-based tools. It uses standard input/output, making it simple and fast for single-machine setups.</p>
                        </div>
                        <div class="bg-purple-50 rounded-xl p-6 border border-purple-200">
                            <h4 class="font-semibold text-purple-800 mb-2 flex items-center">
                                <i class="fas fa-globe text-purple-600 mr-2"></i>
                                SSE (Server-Sent Events)
                            </h4>
                            <p class="text-purple-700 text-sm">Best for remote servers or real-time applications. SSE streams data over HTTP, perfect for progressive responses or cloud-hosted tools.</p>
                        </div>
                    </div>
                    <p class="text-gray-700 mt-4 text-sm italic">Pro Tip: Use stdio for quick prototyping and SSE for production-grade, internet-facing servers.</p>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-blue-700">
                        <i class="fas fa-shield-alt text-blue-500"></i> Securing Your MCP Implementation
                    </h3>
                    <p class="text-gray-700 mb-4">To ensure your MCP server is secure, adopt these best practices:</p>
                    <ul class="space-y-3 text-gray-700">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                            <div>
                                <strong>Authentication & Authorization:</strong> Use robust mechanisms to verify users and restrict access.
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                            <div>
                                <strong>Session Management:</strong> Use strong, non-deterministic session IDs and consider binding them to user-specific information.
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                            <div>
                                <strong>Sandboxing:</strong> Isolate MCP servers (local or remote) to limit exposure to untrusted content.
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                            <div>
                                <strong>Input Validation:</strong> Validate prompts and verify tool integrity to prevent malicious inputs.
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                            <div>
                                <strong>Least Privilege:</strong> Grant minimal permissions to MCP components and agents.
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                            <div>
                                <strong>Monitoring & Logging:</strong> Implement systems to detect and log suspicious activities.
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                            <div>
                                <strong>User Confirmation:</strong> Require explicit user approval for sensitive actions triggered by the agent.
                            </div>
                        </li>
                    </ul>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-blue-700">
                        <i class="fas fa-rocket text-blue-500"></i> Next Steps
                    </h3>
                    <ul class="space-y-3 text-gray-700">
                        <li class="flex items-start">
                            <i class="fas fa-arrow-right text-blue-500 mt-1 mr-3"></i>
                            <div><strong>Enhance Your Tool:</strong> Replace the mock rates with a real API (e.g., exchangerate-api.com).</div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-arrow-right text-blue-500 mt-1 mr-3"></i>
                            <div><strong>Explore More Tools:</strong> Add more MCP tools for tasks like stock lookups or weather updates.</div>
                        </li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- RAG Section -->
        <section id="rag" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold bg-gradient-to-r from-purple-400 to-purple-700 bg-clip-text text-transparent flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-purple-200">
                    <i class="fas fa-database text-purple-600"></i> RAG (Retrieval Augmented Generation)
                </h2>
            </div>
            <div class="content-block-bg rounded-3xl shadow-xl ring-2 ring-purple-100 p-10 mb-10 border border-purple-200">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-purple-700">
                        <i class="fas fa-info-circle text-purple-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">RAG (Retrieval Augmented Generation) is a pattern that enhances LLM responses by incorporating relevant information from external knowledge sources. This pattern is particularly useful for grounding LLM outputs in factual data and providing up-to-date information. RAG combines the power of information retrieval with generative AI to produce more accurate and contextually relevant responses.</p>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-purple-700">
                        <i class="fas fa-code text-purple-500"></i> Implementation Example
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code># Pseudocode for RAG (Retrieval Augmented Generation) Pipeline

# 1. Store documents with embeddings in a vector store
vector_store = VectorStore()
for doc in documents:
    embedding = embed(doc)
    vector_store.add(doc, embedding)

# 2. When a query comes in:
def process_query(query):
    # a. Embed the query
    query_embedding = embed(query)
    # b. Retrieve top-k similar documents
    relevant_docs = vector_store.search(query_embedding, top_k)
    # c. Concatenate retrieved docs as context
    context = join([doc.content for doc in relevant_docs])
    # d. Pass query and context to LLM
generated_response = llm_generate(query, context)
    # e. Return the response
    return generated_response

# Example usage
response = process_query("What is RAG?")
print(response)
</code></pre>
                </div>
            </div>
        </section>

        <!-- Agent-to-Agent Section -->
        <section id="a2a" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold bg-gradient-to-r from-green-400 to-green-700 bg-clip-text text-transparent flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-green-200">
                    <i class="fas fa-network-wired text-green-600"></i> Agent-to-Agent Protocol (A2A)
                </h2>
            </div>
            <div class="content-block-bg rounded-3xl shadow-xl ring-2 ring-green-100 p-10 mb-10 border border-green-200">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-green-700">
                        <i class="fas fa-info-circle text-green-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">Google's A2A is an open protocol designed for horizontal integration – secure and standardized communication between diverse AI agents across different platforms and vendors. This protocol allows agents from different platforms and vendors to discover each other's capabilities, exchange messages, and collaborate on tasks. It uses signed messages and capability discovery to ensure secure cross-platform interactions, making it ideal for distributed agent systems and cloud-based AI applications. Note that this is different from local multi-agent frameworks like CrewAI and AutoGen, which focus on coordinating agents within a single system.</p>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-green-700">
                        <i class="fas fa-puzzle-piece text-green-500"></i> Key Communication Protocols
                    </h3>
                    <ul class="space-y-4 text-gray-700">
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Capability Discovery (Agent Cards)</h4>
                            <ul class="list-disc list-inside ml-4 space-y-2">
                                <li>Agents publish public JSON "Agent Cards" (e.g., at /.well-known/agent.json) describing their functionalities, endpoints, security, and data formats.</li>
                                <li>Client agents dynamically discover and select suitable remote agents based on these cards.</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Task Delegation</h4>
                            <ul class="list-disc list-inside ml-4 space-y-2">
                                <li>A client agent sends a "Task" (a specific work unit with a unique ID) to a remote agent.</li>
                                <li>Tasks include metadata and a "message" containing details (text, files, etc.).</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Structured Messaging & Artifacts</h4>
                            <ul class="list-disc list-inside ml-4 space-y-2">
                                <li>Communication uses signed, structured messages based on a shared schema.</li>
                                <li>Results are returned as "Artifacts," which can be multi-part (e.g., text, files), crucial for rich data exchange.</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Asynchronous Operations & Notifications</h4>
                            <ul class="list-disc list-inside ml-4 space-y-2">
                                <li>Supports long-running tasks, with clients receiving updates via push notifications or Server-Sent Events (SSE).</li>
                            </ul>
                        </li>
                    </ul>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-green-700">
                        <i class="fas fa-code text-green-500"></i> Conceptual Example: A2A Interaction
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code># Pseudocode for A2A (Agent-to-Agent) Interaction

# 1. Remote agent publishes its capabilities (agent card)
remote_agent_card = {
    "id": "data-processor-001",
    "capabilities": ["process_data", "generate_report"],
    "api_url": "https://api.example.com/agents/data-processor-001"
}

# 2. Client agent discovers remote agent
client_agent.discover(remote_agent_card)

# 3. Client agent delegates a task to the remote agent
task = {
    "id": generate_unique_id(),
    "capability": "process_data",
    "message": {
        "data_type": "sales_report",
        "format": "json",
        "parameters": {"region": "EMEA", "period": "Q3"}
    },
    "metadata": {"sender": client_agent.id, "timestamp": now()}
}

result = remote_agent.process_task(task)

# 4. Client agent receives the result
print(result)
</code></pre>
                </div>
            </div>
        </section>

        <!-- Agent Communication Protocol Section -->
        <section id="acp" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold bg-gradient-to-r from-yellow-400 to-yellow-700 bg-clip-text text-transparent flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-yellow-200">
                    <i class="fas fa-exchange-alt text-yellow-600"></i> Agent Communication Protocol (ACP)
                </h2>
            </div>
            <div class="content-block-bg rounded-3xl shadow-xl ring-2 ring-yellow-100 p-10 mb-10 border border-yellow-200">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-yellow-700">
                        <i class="fas fa-info-circle text-yellow-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">Agent Communication Protocol (ACP) provides a structured framework for agent interactions, focusing on local-first, REST-native communication. It enables secure and efficient dialogue between agents through standardized message formats, event-driven architecture, and metadata management. This protocol is particularly useful for privacy-sensitive scenarios and edge computing applications.</p>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-yellow-700">
                        <i class="fas fa-puzzle-piece text-yellow-500"></i> Key Communication Protocols
                    </h3>
                    <ul class="space-y-4 text-gray-700">
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">REST-native Messaging</h4>
                            <ul class="list-disc list-inside ml-4 space-y-2">
                                <li>Standard HTTP methods for agent communication.</li>
                                <li>Structured request/response formats for clear interaction patterns.</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Event-Driven Architecture</h4>
                            <ul class="list-disc list-inside ml-4 space-y-2">
                                <li>Asynchronous communication through event publishing and subscription.</li>
                                <li>Support for real-time updates and notifications.</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Structured Dialogue</h4>
                            <ul class="list-disc list-inside ml-4 space-y-2">
                                <li>Standardized message formats for different types of interactions.</li>
                                <li>Support for multi-turn conversations and context management.</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Security & Authentication</h4>
                            <ul class="list-disc list-inside ml-4 space-y-2">
                                <li>Built-in support for authentication and authorization.</li>
                                <li>Secure message exchange and data protection.</li>
                            </ul>
                        </li>
                        <li class="mb-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Message Performative Semantics (FIPA-inspired)</h4>
                            <ul class="list-disc list-inside ml-4 space-y-2">
                                <li>Messages include a <strong>performative</strong> field to indicate intent, such as: <code>inform</code>, <code>request</code>, <code>agree</code>, <code>refuse</code>, <code>propose</code>, <code>inform-result</code>.</li>
                                <li>This helps agents interpret the purpose of a message and respond appropriately.</li>
                                <li>Example: <code>message_type="request"  # message.performative = "request"</code></li>
                            </ul>
                        </li>
                    </ul>
                </div>

                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-yellow-700">
                        <i class="fas fa-code text-yellow-500"></i> Conceptual Example: ACP Communication
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code># Pseudocode for ACP (Agent Communication Protocol) Communication

class ACPAgent:
    def __init__(self, id):
        self.id = id
        self.handlers = {}
    def register_handler(self, message_type, handler):
        self.handlers[message_type] = handler
    def receive_message(self, message):
        # Auto-dispatch based on performative / type
        handler = self.handlers.get(getattr(message, 'performative', None) or message.message_type)
        if handler:
            handler(message)

# 1. Define two agents with unique IDs
agent1 = ACPAgent("agent1")
agent2 = ACPAgent("agent2")

# 2. Register a handler for request messages on agent1
def handle_request(message):
    # Process the request and prepare a response
    response = ACPMessage(
        message_type="response",
        performative="inform-result",  # FIPA-inspired performative
        content={"status": "success", "data": "Request processed"},
        sender_id=agent1.id,
        recipient_id=message.sender_id
    )
    agent1.send_message(response)

agent1.register_handler("request", handle_request)

# 3. agent2 sends a request message to agent1
request = ACPMessage(
    message_type="request",
    performative="request",  # FIPA-inspired performative
    content={"action": "process_data", "data": "sample data"},
    sender_id=agent2.id,
    recipient_id=agent1.id
)
agent2.send_message(request)

# 4. agent1 receives and handles the request
agent1.receive_message(request)
</code></pre>
                </div>
            </div>
        </section>
    </div>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-analytics-compat.js"></script>
    <div id="footer-placeholder"></div>
    <script type="module" src="../js/common-components.js"></script>
    <script type="module" src="../js/app.js"></script>
</body>
</html>
