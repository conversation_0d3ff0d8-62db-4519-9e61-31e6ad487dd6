<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agentic Workflow Patterns</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/custom-styles.css">
</head>
<body class="bg-gray-50 text-gray-800">
    <div id="header-placeholder"></div>
    <div class="max-w-6xl mx-auto px-4 py-8">
        <!-- Navigation -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-10 gap-3">
            <a href="agentic-core-patterns.html" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold py-2 px-4 rounded-lg bg-blue-50 hover:bg-blue-100 transition duration-300 shadow-sm border border-blue-200">
                <i class="fas fa-arrow-left mr-2"></i> Previous: Core Patterns
            </a>
            <a href="../../learning-hub.html" class="inline-flex items-center text-gray-700 hover:text-blue-700 font-semibold py-2 px-4 rounded-lg bg-gray-100 hover:bg-gray-200 transition duration-300 shadow-sm border border-gray-300">
                <i class="fas fa-home mr-2"></i> Home
            </a>
            <a href="agentic-communication-patterns.html" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold py-2 px-4 rounded-lg bg-blue-50 hover:bg-blue-100 transition duration-300 shadow-sm border border-blue-200">
                Next: Communication <i class="fas fa-arrow-right ml-2"></i>
            </a>
        </div>
        
        <header class="header-gradient rounded-3xl shadow-2xl p-12 mb-16 text-center border border-blue-300 transform transition-all duration-500 hover:scale-[1.01] hover:shadow-3xl relative overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-100/60 to-indigo-100/40 pointer-events-none"></div>
            <div class="relative z-10">
                <h1 class="text-5xl md:text-6xl font-extrabold text-blue-900 mb-4 tracking-tight drop-shadow-lg leading-tight">
                    Agentic Workflow Patterns
                </h1>
                <p class="text-2xl text-blue-700 font-medium opacity-90 mb-4">
                    Patterns for orchestrating complex, reliable, and scalable multi-agent workflows
                </p>
                <p class="max-w-3xl mx-auto text-lg text-blue-800/90 mb-2">
                    These patterns go beyond single-agent logic, providing blueprints for building robust systems of collaborating LLM agents, tools, and services. Mastering these will help you design AI solutions that are modular, resilient, and enterprise-ready.
                </p>
            </div>
        </header>

        <!-- Pattern Navigation Tiles -->
        <nav class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <a href="#pipeline" class="bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-blue-200 hover:ring-blue-300 transform hover:scale-105">
                <div class="text-5xl text-blue-600 mb-3"><i class="fas fa-project-diagram"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-blue-800 text-center">Pipeline</h3>
                <p class="text-gray-600 text-center text-sm">Sequential task processing</p>
            </a>

            <a href="#orchestration" class="bg-gradient-to-br from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-purple-200 hover:ring-purple-300 transform hover:scale-105">
                <div class="text-5xl text-purple-600 mb-3"><i class="fas fa-music"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-purple-800 text-center">Orchestration</h3>
                <p class="text-gray-600 text-center text-sm">Centralized workflow control</p>
            </a>

            <a href="#choreography" class="bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-green-200 hover:ring-green-300 transform hover:scale-105">
                <div class="text-5xl text-green-600 mb-3"><i class="fas fa-random"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-green-800 text-center">Choreography</h3>
                <p class="text-gray-600 text-center text-sm">Decentralized workflow coordination</p>
            </a>

            <a href="#saga" class="bg-gradient-to-br from-yellow-50 to-yellow-100 hover:from-yellow-100 hover:to-yellow-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-yellow-200 hover:ring-yellow-300 transform hover:scale-105">
                <div class="text-5xl text-yellow-600 mb-3"><i class="fas fa-book"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-yellow-800 text-center">Saga</h3>
                <p class="text-gray-600 text-center text-sm">Distributed transaction management</p>
            </a>

            <a href="#cqrs" class="bg-gradient-to-br from-indigo-50 to-indigo-100 hover:from-indigo-100 hover:to-indigo-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-indigo-200 hover:ring-indigo-300 transform hover:scale-105 relative">
                <div class="text-5xl text-indigo-600 mb-3"><i class="fas fa-exchange-alt"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-indigo-800 text-center">CQRS</h3>
                <span class="inline-block bg-indigo-200 text-indigo-800 text-xs font-semibold px-3 py-1 rounded-full mb-2">Supporting Pattern</span>
                <p class="text-gray-600 text-center text-sm">Read/write separation for knowledge agents</p>
            </a>
        </nav>

        <section id="pipeline" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold bg-gradient-to-r from-blue-400 to-blue-700 bg-clip-text text-transparent flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-blue-200">
                    <i class="fas fa-project-diagram text-blue-600"></i> Pipeline
                </h2>
            </div>
            <div class="content-block-bg rounded-3xl shadow-xl ring-2 ring-blue-100 p-10 mb-10 border border-blue-200">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-blue-700">
                        <i class="fas fa-info-circle text-blue-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">A linear sequence of processing stages where the output of one stage is the input to the next. Each stage performs a specific transformation or analysis on the data. This pattern is particularly relevant for LLM agents as it directly parallels the concept of "Prompt Chaining" - the fundamental way to structure multi-step LLM tasks.</p>
                    <p class="text-gray-700 leading-relaxed">Example LLM workflow: Define Goal → Search Web → Synthesize Findings → Draft Report → Review & Edit</p>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-blue-700">
                        <i class="fas fa-puzzle-piece text-blue-500"></i> Key Features
                    </h3>
                    <ul class="list-disc list-inside text-gray-700 ml-6 mt-4 space-y-2">
                        <li>Stages: Processing steps</li>
                        <li>Data Flow: Information transfer</li>
                        <li>Error Handling: Failure management</li>
                        <li>Monitoring: Performance tracking</li>
                    </ul>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-blue-700">
                        <i class="fas fa-code text-blue-500"></i> Implementation Example
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code class="language-python"># Mock LLM (replace with PipelineAI or your LLM)
llm = PipelineAI(pipeline_key="public/gpt-j:base")

# 1. Define Goal
goal_prompt = PromptTemplate.from_template("""Define the main goal for this research task:\n{input}""")
# 2. Search Web
search_prompt = PromptTemplate.from_template("""Search the web for information about: {goal}""")
# 3. Synthesize Findings
synthesize_prompt = PromptTemplate.from_template("""Summarize key findings from the search:\n{search_results}""")
# 4. Draft Report
draft_prompt = PromptTemplate.from_template("""Draft a report based on the findings:\n{summary}""")
# 5. Review & Edit
review_prompt = PromptTemplate.from_template("""Review and edit the report for clarity and completeness:\n{draft}""")

# Chain the steps
pipeline = RunnableSequence([
    goal_prompt | llm | StrOutputParser(),
    search_prompt | llm | StrOutputParser(),
    synthesize_prompt | llm | StrOutputParser(),
    draft_prompt | llm | StrOutputParser(),
    review_prompt | llm | StrOutputParser(),
])

# Run the pipeline
input_text = "How can AI improve supply chain efficiency?"
result = pipeline.invoke({"input": input_text})
print(result)
</code></pre>
                </div>
            </div>
        </section>

        <section id="orchestration" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold bg-gradient-to-r from-purple-400 to-purple-700 bg-clip-text text-transparent flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-purple-200">
                    <i class="fas fa-music text-purple-600"></i> Manager-Worker (Hierarchical) Orchestration
                </h2>
            </div>
            <div class="content-block-bg rounded-3xl shadow-xl ring-2 ring-purple-100 p-10 mb-10 border border-purple-200">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-purple-700">
                        <i class="fas fa-info-circle text-purple-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">This pattern features a single, central <span class="font-bold">manager agent</span> that directs the workflow by decomposing the overall task into subtasks and delegating them to specialized <span class="font-bold">worker agents</span>. Each worker agent is responsible for a specific aspect of the process. The manager coordinates the sequencing, collects outputs from workers, and consolidates the results for the user.</p>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-purple-700">
                        <i class="fas fa-puzzle-piece text-purple-500"></i> Key Features
                    </h3>
                    <ul class="list-disc list-inside text-gray-700 ml-6 mt-4 space-y-2">
                        <li><span class="font-bold">Central Orchestrator:</span> A single "manager" agent directs the workflow.</li>
                        <li><span class="font-bold">Task Delegation:</span> The manager decomposes the overall task into subtasks.</li>
                        <li><span class="font-bold">Specialized Agents:</span> Dedicated "worker" agents handle specific responsibilities (e.g., flight booking, hotel booking).</li>
                        <li><span class="font-bold">Coordination:</span> The orchestrator manages sequencing, collects outputs, and consolidates results.</li>
                    </ul>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-purple-700">
                        <i class="fas fa-code text-purple-500"></i> Implementation Example
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code class="language-python"># agent_base.py
class Agent:
    def __init__(self, name):
        self.name = name

    def handle_task(self, task):
        raise NotImplementedError("Each agent must implement handle_task.")

# flight_booker.py
from agent_base import Agent

class FlightBookerAgent(Agent):
    def handle_task(self, task):
        if task.get("type") == "book_flight":
            destination = task.get("data", {}).get("destination", "Unknown")
            return f"Flight booked to {destination} (Air France 345)"
        return "FlightBooker: Task not recognized"

# hotel_booker.py
from agent_base import Agent

class HotelBookerAgent(Agent):
    def handle_task(self, task):
        if task.get("type") == "book_hotel":
            destination = task.get("data", {}).get("destination", "Unknown")
            return f"Hotel booked in {destination} (Hilton Garden Inn)"
        return "HotelBooker: Task not recognized"

# travel_manager.py
from flight_booker import FlightBookerAgent
from hotel_booker import HotelBookerAgent

class TravelManagerAgent:
    def __init__(self):
        self.flight_agent = FlightBookerAgent("FlightBooker")
        self.hotel_agent = HotelBookerAgent("HotelBooker")

    def handle_travel_request(self, destination):
        print(f"[Manager] Received travel request to {destination}")

        # Decompose the task
        flight_task = {"type": "book_flight", "data": {"destination": destination}}
        hotel_task = {"type": "book_hotel", "data": {"destination": destination}}

        # Delegate to workers
        print("[Manager] Delegating flight booking...")
        flight_result = self.flight_agent.handle_task(flight_task)

        print("[Manager] Delegating hotel booking...")
        hotel_result = self.hotel_agent.handle_task(hotel_task)

        # Combine results
        return {
            "flight": flight_result,
            "hotel": hotel_result,
            "summary": f"Trip to {destination} booked: ✈️ + 🏨"
        }

# main.py
from travel_manager import TravelManagerAgent

if __name__ == "__main__":
    manager = TravelManagerAgent()
    result = manager.handle_travel_request("Paris")

    print("\n✅ Final Result:")
    print(result["summary"])
    print(result["flight"])
    print(result["hotel"])
</code></pre>
                    <div class="bg-gray-800 text-green-200 rounded-xl p-4 mt-4 text-sm font-mono">
                        <span class="font-bold">🧪 Output</span>
                        <pre class="whitespace-pre-line">[Manager] Received travel request to Paris
[Manager] Delegating flight booking...
[Manager] Delegating hotel booking...

✅ Final Result:
Trip to Paris booked: ✈️ + 🏨
Flight booked to Paris (Air France 345)
Hotel booked in Paris (Hilton Garden Inn)</pre>
                    </div>
                </div>
            </div>
        </section>

        <section id="choreography" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold bg-gradient-to-r from-green-400 to-green-700 bg-clip-text text-transparent flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-green-200">
                    <i class="fas fa-random text-green-600"></i> Choreography
                </h2>
            </div>
            <div class="content-block-bg rounded-3xl shadow-xl ring-2 ring-green-100 p-10 mb-10 border border-green-200">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-green-700">
                        <i class="fas fa-info-circle text-green-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">A decentralized workflow pattern where agents interact directly with each other without central coordination, following predefined interaction patterns. This enables more decentralized, event-driven architectures where agents react to events and messages.</p>
                    <p class="text-gray-700 leading-relaxed">Example: A "customer support" agent creates a "ticket" event, which a "technical agent" then picks up without being directly commanded by the first agent.</p>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-green-700">
                        <i class="fas fa-puzzle-piece text-green-500"></i> Key Features
                    </h3>
                    <ul class="list-disc list-inside text-gray-700 ml-6 mt-4 space-y-2">
                        <li><span class="font-bold">No Central Manager:</span> Agents operate independently, no supervisor agent needed</li>
                        <li><span class="font-bold">Event-Driven:</span> Agents react to events (e.g. message queues, signals)</li>
                        <li><span class="font-bold">Peer-to-Peer Links:</span> Agents talk to each other using predefined protocols</li>
                        <li><span class="font-bold">Loose Coupling:</span> Agents are decoupled and can evolve independently</li>
                    </ul>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-green-700">
                        <i class="fas fa-code text-green-500"></i> Implementation Example
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code class="language-python"># Agent implementations (typically, each could wrap an LLM prompt/tool API)
class CustomerSupportAgent:
    def __init__(self, bus):
        self.bus = bus

    def handle_user_issue(self, issue):
        print(f"[Support] User reports: {issue}")
        event = {"ticket_id": 1, "issue": issue}
        self.bus.emit("ticket_created", event)

class TechnicalAgent:
    def __init__(self, bus):
        self.bus = bus
        self.bus.subscribe("ticket_created", self.fix_issue)

    def fix_issue(self, event):
        print(f"[Tech] Working on ticket #{event['ticket_id']}: {event['issue']}")
        # Would use an LLM here for troubleshooting steps!
        self.bus.emit("ticket_resolved", event)

class NotificationAgent:
    def __init__(self, bus):
        self.bus = bus
        self.bus.subscribe("ticket_resolved", self.notify_user)

    def notify_user(self, event):
        print(f"[Notification] Ticket #{event['ticket_id']} resolved.")

# Set up system
bus = EventBus()
support = CustomerSupportAgent(bus)
tech = TechnicalAgent(bus)
notifier = NotificationAgent(bus)

# Simulate a support request
support.handle_user_issue("Cannot access account.")
</code></pre>
                    <div class="bg-gray-800 text-green-200 rounded-xl p-4 mt-4 text-sm font-mono">
                        <span class="font-bold">🧾 Output Example</span>
                        <pre class="whitespace-pre-line">[Support] User reports: Cannot access account.
[Tech] Working on ticket #1: Cannot access account.
[Notification] Ticket #1 resolved.
</pre>
                    </div>
                </div>
            </div>
        </section>

        <section id="saga" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold bg-gradient-to-r from-yellow-400 to-yellow-700 bg-clip-text text-transparent flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-yellow-200">
                    <i class="fas fa-book text-yellow-600"></i> Saga
                </h2>
            </div>
            <div class="content-block-bg rounded-3xl shadow-xl ring-2 ring-yellow-100 p-10 mb-10 border border-yellow-200">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-yellow-700">
                        <i class="fas fa-info-circle text-yellow-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">The <span class="font-bold">Saga</span> pattern is a robust workflow strategy for managing distributed, multi-step processes. It breaks down a large transaction into a series of local steps, each with a compensating action to recover from failures. This ensures that even if part of a workflow fails, the system can gracefully revert to a consistent state—crucial for LLM agent workflows that span multiple services or actions.</p>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-yellow-700">
                        <i class="fas fa-puzzle-piece text-yellow-500"></i> Key Features
                    </h3>
                    <ul class="list-disc list-inside text-gray-700 ml-6 mt-4 space-y-2">
                        <li><span class="font-bold">Stepwise Execution:</span> Breaks down complex workflows into manageable, local transactions.</li>
                        <li><span class="font-bold">Compensating Actions:</span> Each step has an "undo" operation to maintain consistency if a failure occurs.</li>
                        <li><span class="font-bold">Graceful Recovery:</span> If a step fails, compensating actions are triggered in reverse order to roll back changes.</li>
                        <li><span class="font-bold">Reliability:</span> Ensures no partial or inconsistent state is left behind, even in distributed LLM agent systems.</li>
                    </ul>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-yellow-700">
                        <i class="fas fa-code text-yellow-500"></i> Implementation Example (LLM Agent Trip Booking)
                    </h3>
                    <div class="mb-4">
                        <h4 class="font-bold text-lg text-yellow-700 mb-2">Step-by-Step Workflow</h4>
                        <ol class="list-decimal list-inside text-gray-700 ml-6 mb-4 space-y-1">
                            <li><span class="font-bold">Book Flight:</span> The agent books a flight for the user.</li>
                            <li><span class="font-bold">Book Hotel:</span> The agent attempts to book a hotel.</li>
                            <li><span class="font-bold">Failure Handling:</span> If the hotel is unavailable, the agent triggers a compensating action to cancel the flight booking.</li>
                        </ol>
                    </div>
                    <div class="mb-4">
                        <h4 class="font-bold text-lg text-yellow-700 mb-2">Pseudocode</h4>
                        <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-yellow-700"><code class="language-python">def book_trip(destination):
    try:
        flight = book_flight(destination)  # Step 1
        try:
            hotel = book_hotel(destination)  # Step 2
            return {"flight": flight, "hotel": hotel}
        except HotelFullException:
            cancel_flight(flight)  # Compensating action
            raise
    except Exception as e:
        # Handle global failure
        raise e
</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section id="cqrs" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold bg-gradient-to-r from-indigo-400 to-indigo-700 bg-clip-text text-transparent flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-indigo-200">
                    <i class="fas fa-exchange-alt text-indigo-600"></i> CQRS
                </h2>
            </div>
            <div class="content-block-bg rounded-3xl shadow-xl ring-2 ring-indigo-100 p-10 mb-10 border border-indigo-200">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-indigo-700">
                        <i class="fas fa-info-circle text-indigo-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">The <span class="font-bold">Command Query Responsibility Segregation (CQRS)</span> pattern supercharges knowledge management agents by splitting the system into two distinct flows: one for learning (write/command) and one for answering (read/query). This separation allows each side to be optimized for its unique needs—ensuring blazing-fast answers for users and robust, reliable knowledge updates in the background.</p>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-indigo-700">
                        <i class="fas fa-puzzle-piece text-indigo-500"></i> Key Features
                    </h3>
                    <ul class="list-disc list-inside text-gray-700 ml-6 mt-4 space-y-2">
                        <li><span class="font-bold">Performance:</span> Reads and writes are independently optimized, avoiding bottlenecks.</li>
                        <li><span class="font-bold">Consistency:</span> Complex updates don’t block fast queries.</li>
                        <li><span class="font-bold">Scalability:</span> Each side can scale out (horizontally) as needed.</li>
                        <li><span class="font-bold">Auditability:</span> Clear separation between knowledge ingestion (learning) and knowledge use (answering).</li>
                    </ul>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-indigo-700">
                        <i class="fas fa-code text-indigo-500"></i> Implementation Example (LLM Knowledge Management)
                    </h3>
                    <div class="mb-4">
                        <h4 class="font-bold text-lg text-indigo-700 mb-2">How CQRS Works</h4>
                        <table class="min-w-full text-left text-gray-700 mb-6 border border-indigo-200 rounded-xl">
                            <thead>
                                <tr>
                                    <th class="pr-6 pb-2 font-bold">Operation Side</th>
                                    <th class="pr-6 pb-2 font-bold">Purpose</th>
                                    <th class="pb-2 font-bold">Characteristics</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="bg-indigo-50">
                                    <td class="pr-6">Command (Write)</td>
                                    <td class="pr-6">Learn & Update</td>
                                    <td>Slower, complex, consistency-focused</td>
                                </tr>
                                <tr>
                                    <td class="pr-6">Query (Read)</td>
                                    <td class="pr-6">Answer & Retrieve</td>
                                    <td>Fast, scalable, optimized-for-search</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="mb-4">
                        <h4 class="font-bold text-lg text-indigo-700 mb-2">Typical Agent Workflows</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <div class="font-bold text-indigo-600 mb-1">Command Side (Write)</div>
                                <ul class="list-disc list-inside text-gray-700 ml-4 space-y-1">
                                    <li>Ingests new documents (PDFs, articles, notes)</li>
                                    <li>Extracts entities, relationships, and insights via LLMs</li>
                                    <li>Updates the knowledge base and refreshes embeddings/indexes</li>
                                    <li>Resource-intensive, but enables up-to-date knowledge</li>
                                </ul>
                            </div>
                            <div>
                                <div class="font-bold text-indigo-600 mb-1">Query Side (Read)</div>
                                <ul class="list-disc list-inside text-gray-700 ml-4 space-y-1">
                                    <li>Receives user queries in natural language</li>
                                    <li>Retrieves relevant knowledge instantly using indexes/embeddings</li>
                                    <li>Generates answers with LLMs, summaries, or search-based techniques</li>
                                    <li>Optimized for speed and user experience</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <h4 class="font-bold text-lg text-indigo-700 mb-2">Real-World Example</h4>
                        <ul class="list-decimal list-inside text-gray-700 ml-6 mb-4 space-y-1">
                            <li><span class="font-bold">Command:</span> Analyst uploads a new industry report</li>
                            <li><span class="font-bold">Command:</span> Agent extracts facts, tags insights, updates database and indexes</li>
                            <li><span class="font-bold">Query:</span> User asks, “What are the latest trends in renewable energy?”</li>
                            <li><span class="font-bold">Query:</span> Agent retrieves and summarizes the latest information</li>
                        </ul>
                    </div>
                </div>
        </section>
    </div>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-analytics-compat.js"></script>
    <script type="module" src="../js/common-components.js"></script>
    <script type="module" src="../js/app.js"></script>
</body>
</html>
