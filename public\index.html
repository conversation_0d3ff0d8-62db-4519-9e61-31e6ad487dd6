<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="RebootingwithAI - My personal journey through the AI revolution, exploring enterprise architecture and intelligent systems.">
    <meta name="author" content="RebootingwithAI">
    <title>RebootingwithAI - My Journey Through the AI Revolution</title>

    <!-- Updated Tailwind CSS CDN for JIT mode -->
    <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
      /* Apply Inter font globally */
      body {
        font-family: 'Inter', sans-serif;
      }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 antialiased">
    <div id="header-placeholder"></div>

    <!-- Hero Section -->
    <section id="home" class="bg-gradient-to-br from-blue-700 to-purple-800 text-white py-20 md:py-28 lg:py-32">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-5xl lg:text-6xl font-extrabold mb-6 leading-tight">
                <span class="text-yellow-300 block mb-2">RebootingwithAI</span>
                <span class="text-white text-3xl md:text-4xl lg:text-5xl font-bold">My Journey Through the AI Revolution</span>
            </h1>
            <p class="text-lg md:text-xl lg:text-2xl mb-10 max-w-3xl mx-auto opacity-90 leading-relaxed">
                Exploring artificial intelligence, enterprise architecture, and intelligent systems.
            </p>

            <div class="flex justify-center gap-8 mb-12">
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-yellow-300 mb-1" data-stat="blog" data-add-plus></div>
                    <div class="text-sm opacity-80">Blog Posts</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-yellow-300 mb-1" data-stat="journey" data-add-plus></div>
                    <div class="text-sm opacity-80">Years Journey</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl md:text-3xl font-bold text-yellow-300 mb-1" data-stat="keyOfferings" data-add-plus></div>
                    <div class="text-sm opacity-80">Case Studies</div>
                </div>
            </div>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="learning-hub.html" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl">
                    <i class="fas fa-network-wired mr-2"></i>
                    Explore Agentic Architecture
                </a>
            </div>
        </div>
    </section>

    <!-- Content Hub Section -->
    <section id="content-hub" class="py-16 md:py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Content Hub</h2>
                <p class="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto">
                    Technical insights, case studies, and industry analysis from my AI journey.
                </p>
            </div>

            <div class="max-w-2xl mx-auto">
                <!-- Combined Blog & Insights Card -->
                <div class="bg-gradient-to-br from-blue-50 via-purple-50 to-blue-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                    <div class="text-center">
                        <div class="flex justify-center items-center mb-4">
                            <i class="fas fa-blog text-3xl text-blue-600 mr-3"></i>
                            <i class="fas fa-newspaper text-3xl text-purple-600"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-3">Blog & Insights</h3>
                        <p class="text-gray-600 mb-4">Deep dives into AI implementation, enterprise architecture patterns, system design, and industry analysis. Explore both technical blog posts and commentary on AI trends.</p>
                        <div class="flex flex-wrap justify-center gap-2 mb-4">
                            <span class="px-3 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">Blog Posts</span>
                            <span class="px-3 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">Industry News</span>
                            <span class="px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Technical Analysis</span>
                        </div>
                        <a href="src/pages/blog/index.html" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105">
                            Explore All Content <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Connect Section -->
    <section id="connect" class="py-16 md:py-20 bg-gradient-to-b from-gray-50 to-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Let's Connect</h2>
            <p class="text-xl text-gray-600 mb-8">
                Follow my journey and collaborate on AI and enterprise architecture projects.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <!-- YouTube button removed -->
                <a href="mailto:<EMAIL>" class="inline-flex items-center px-6 py-3 border-2 border-gray-600 text-gray-600 rounded-lg hover:bg-gray-600 hover:text-white transition-all duration-300">
                    <i class="fas fa-envelope mr-2"></i>
                    Email Me
                </a>
            </div>
        </div>
    </section>

    <div id="footer-placeholder"></div>

    <!-- Firebase SDK -->
    <script type="importmap">
    {
        "imports": {
            "firebase/app": "https://www.gstatic.com/firebasejs/10.12.2/firebase-app.js",
            "firebase/analytics": "https://www.gstatic.com/firebasejs/10.12.2/firebase-analytics.js",
            "firebase/firestore": "https://www.gstatic.com/firebasejs/10.12.2/firebase-firestore.js",
            "firebase/auth": "https://www.gstatic.com/firebasejs/10.12.2/firebase-auth.js"
        }
    }
    </script>
    
    <!-- Application Scripts -->
    <script type="module" src="src/js/common-components.js"></script>
    <script type="module" src="src/js/app.js"></script>
</body>
</html>