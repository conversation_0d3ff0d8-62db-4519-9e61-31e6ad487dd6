<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agentic Decision Patterns</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/custom-styles.css">
</head>
<body class="bg-gray-50 text-gray-800">
    <div id="header-placeholder"></div>
    <div class="max-w-6xl mx-auto px-4 py-8">
        <!-- Navigation -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-10 gap-3">
            <a href="agentic-communication-patterns.html" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold py-2 px-4 rounded-lg bg-blue-50 hover:bg-blue-100 transition duration-300 shadow-sm border border-blue-200">
                <i class="fas fa-arrow-left mr-2"></i> Previous: Communication
            </a>
            <a href="../../learning-hub.html" class="inline-flex items-center text-gray-700 hover:text-blue-700 font-semibold py-2 px-4 rounded-lg bg-gray-100 hover:bg-gray-200 transition duration-300 shadow-sm border border-gray-300">
                <i class="fas fa-home mr-2"></i> Home
            </a>
            <a href="agentic-learning-patterns.html" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold py-2 px-4 rounded-lg bg-blue-50 hover:bg-blue-100 transition duration-300 shadow-sm border border-blue-200">
                Next: Learning <i class="fas fa-arrow-right ml-2"></i>
            </a>
        </div>
        
        <header class="header-gradient rounded-3xl shadow-2xl p-12 mb-16 text-center border border-blue-300 transform transition-all duration-500 hover:scale-[1.01] hover:shadow-3xl">
            <h1 class="text-5xl md:text-6xl font-extrabold text-blue-900 mb-5 tracking-tight drop-shadow-lg leading-tight">Agentic Decision Patterns</h1>
            <p class="text-2xl text-blue-700 font-medium opacity-90">Decision patterns for agentic systems: enabling LLM agents to make robust, adaptive, and explainable choices for complex problem-solving.</p>
        </header>

        <!-- Pattern Navigation Tiles -->
        <nav class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <a href="#rule-engine" class="bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-blue-200 hover:ring-blue-300 transform hover:scale-105">
                <div class="text-5xl text-blue-600 mb-3"><i class="fas fa-cogs"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-blue-800 text-center">Rule Engine</h3>
                <p class="text-gray-600 text-center text-sm">Rule-based decision making using explicit business rules and conditions</p>
            </a>
            <a href="#reinforcement" class="bg-gradient-to-br from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-purple-200 hover:ring-purple-300 transform hover:scale-105">
                <div class="text-5xl text-purple-600 mb-3"><i class="fas fa-robot"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-purple-800 text-center">Reinforcement Learning</h3>
                <p class="text-gray-600 text-center text-sm">Learning optimal decision-making through trial and error</p>
            </a>
            <a href="#expert-system" class="bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 transition-all duration-300 rounded-2xl p-8 flex flex-col items-center shadow-lg ring-1 ring-green-200 hover:ring-green-300 transform hover:scale-105">
                <div class="text-5xl text-green-600 mb-3"><i class="fas fa-brain"></i></div>
                <h3 class="font-extrabold text-xl mb-2 text-green-800 text-center">Expert System</h3>
                <p class="text-gray-600 text-center text-sm">Knowledge-based decision making using expert rules and facts</p>
            </a>
        </nav>

        <!-- Rule Engine Section -->
        <section id="rule-engine" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold section-header-blue flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-blue-200">
                    <i class="fas fa-cogs text-blue-600"></i> Rule Engine
                </h2>
            </div>
            <div class="bg-white/95 rounded-3xl shadow-xl ring-2 ring-blue-100 p-10 mb-10 border border-blue-200 backdrop-blur-sm">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-blue-700">
                        <i class="fas fa-info-circle text-blue-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">Rule Engine is a pattern that implements structured decision-making through a collection of explicit rules and conditions. In LLM-based agentic systems, a rule engine can be used after the LLM extracts facts or structured data, enabling deterministic business logic or compliance checks. Each rule consists of conditions that, when met, trigger specific actions or decisions. This pattern is particularly useful for implementing complex business logic and deterministic decision-making processes in agentic systems.</p>
                    
                    <p class="text-gray-700 leading-relaxed mt-2">There are several approaches to implementing rule engines:</p>
                    <ul class="list-disc list-inside text-gray-700 ml-6 mt-4 space-y-2">
                        <li><span class="font-bold text-gray-800">Forward Chaining:</span> Rules are evaluated in sequence, with each rule potentially triggering other rules.</li>
                        <li><span class="font-bold text-gray-800">Backward Chaining:</span> Rules are evaluated in reverse, starting from the goal and working backwards.</li>
                        <li><span class="font-bold text-gray-800">Drools:</span> A popular open-source business rule management system that provides a powerful rule engine with complex event processing. <a href="https://www.drools.org/" target="_blank" class="underline text-blue-600 hover:text-blue-800 transition-colors duration-200">Read about Drools</a></li>
                    </ul>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-blue-700">
                        <i class="fas fa-code text-blue-500"></i> Implementation using Drools
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code># Python Pseudocode for Rule Engine

class LoanApplication:
    def __init__(self):
        self.status = None
        self.reason = None
    def approve(self, reason):
        self.status = "APPROVED"
        self.reason = reason
    def reject(self, reason):
        self.status = "REJECTED"
        self.reason = reason

def rule_engine(facts, application):
    if facts["creditScore"] < 600:
        application.reject("Low credit score")
    elif facts["creditScore"] >= 700 and facts["amount"] <= 100000:
        application.approve("Good credit score and reasonable amount")

facts = llm_agent.extract_facts(user_input)  # LLM agent extracts facts
application = LoanApplication()
rule_engine(facts, application)
print(application.status, application.reason)
</code></pre>
                </div>
            </div>
        </section>

        <!-- Reinforcement Learning Section -->
        <section id="reinforcement" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold section-header-purple flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-purple-200">
                    <i class="fas fa-robot text-purple-600"></i> Reinforcement Learning
                </h2>
            </div>
            <div class="bg-white/95 rounded-3xl shadow-xl ring-2 ring-purple-100 p-10 mb-10 border border-purple-200 backdrop-blur-sm">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-purple-700">
                        <i class="fas fa-info-circle text-purple-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">Reinforcement Learning is a pattern that enables agents—including LLM-based agents—to learn optimal decision-making strategies through interaction with their environment or tool use. The agent receives feedback in the form of rewards or penalties, which it uses to improve its decision-making over time. This pattern is particularly valuable for learning complex behaviors and optimizing long-term outcomes in agentic systems, such as tool selection or dialogue strategies.</p>
                    
                    <p class="text-gray-700 leading-relaxed mt-2">There are several approaches to implementing reinforcement learning:</p>
                    <ul class="list-disc list-inside text-gray-700 ml-6 mt-4 space-y-2">
                        <li><span class="font-bold text-gray-800">Q-Learning:</span> Value-based method for learning optimal action-selection policies.</li>
                        <li><span class="font-bold text-gray-800">Policy Gradient:</span> Direct optimization of policy parameters for better performance.</li>
                        <li><span class="font-bold text-gray-800">Stable Baselines3:</span> A set of reliable implementations of reinforcement learning algorithms in PyTorch. <a href="https://stable-baselines3.readthedocs.io/" target="_blank" class="underline text-purple-600 hover:text-purple-800 transition-colors duration-200">Read about Stable Baselines3</a></li>
                        <li><span class="font-bold text-gray-800">Ray RLlib:</span> A scalable reinforcement learning library that supports distributed training. <a href="https://docs.ray.io/en/latest/rllib/index.html" target="_blank" class="underline text-purple-600 hover:text-purple-800 transition-colors duration-200">Learn about Ray RLlib</a></li>
                    </ul>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-purple-700">
                        <i class="fas fa-code text-purple-500"></i> Implementation using Stable Baselines3
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code># Python Pseudocode for Reinforcement Learning

class Agent:
    def select_action(self, state):
        # LLM agent could use RL to select next tool or response
        pass
    def learn(self, state, action, reward, next_state, done):
        # Update policy based on feedback
        pass
    def save(self, path):
        # Save model
        pass

env = create_environment()
agent = Agent()
num_episodes = 100

for episode in range(num_episodes):
    state = env.reset()
    done = False
    while not done:
        action = agent.select_action(state)
        next_state, reward, done, info = env.step(action)
        agent.learn(state, action, reward, next_state, done)
        state = next_state

agent.save('model_path')
</code></pre>
                </div>
            </div>
        </section>

        <!-- Expert System Section -->
        <section id="expert-system" class="mb-20">
            <div class="mb-8">
                <h2 class="text-4xl font-extrabold section-header-green flex items-center gap-3 drop-shadow-md pb-2 border-b-2 border-green-200">
                    <i class="fas fa-brain text-green-600"></i> Expert System
                </h2>
            </div>
            <div class="bg-white/95 rounded-3xl shadow-xl ring-2 ring-green-100 p-10 mb-10 border border-green-200 backdrop-blur-sm">
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-green-700">
                        <i class="fas fa-info-circle text-green-500"></i> Description
                    </h3>
                    <p class="text-gray-700 leading-relaxed mb-4">Expert System is a pattern that emulates human decision-making by using a knowledge base of expert rules and facts. In LLM-based agentic systems, an expert system can be queried by the agent to provide grounded, explainable decisions or to supplement generative reasoning with symbolic inference. The system uses an inference engine to apply these rules to specific situations and reach conclusions. This pattern is particularly useful for implementing domain-specific decision-making capabilities in agentic systems.</p>
                    
                    <p class="text-gray-700 leading-relaxed mt-2">There are several approaches to implementing expert systems:</p>
                    <ul class="list-disc list-inside text-gray-700 ml-6 mt-4 space-y-2">
                        <li><span class="font-bold text-gray-800">Rule-Based Systems:</span> Using if-then rules to represent expert knowledge. <span class="italic">(See the Rule Engine section above for details.)</span></li>
                        <li><span class="font-bold text-gray-800">Case-Based Reasoning:</span> Solving new problems based on past experiences.</li>
                        <li><span class="font-bold text-gray-800">Neo4j:</span> A graph database that provides powerful tools for building knowledge graphs and expert systems. <a href="https://neo4j.com/developer/kb/" target="_blank" class="underline text-green-600 hover:text-green-800 transition-colors duration-200">Read about Neo4j</a></li>
                        <li><span class="font-bold text-gray-800">Protégé:</span> An open-source ontology editor and knowledge management system. <a href="https://protege.stanford.edu/" target="_blank" class="underline text-green-600 hover:text-green-800 transition-colors duration-200">Learn about Protégé</a></li>
                    </ul>
                </div>
                <div class="mb-10">
                    <h3 class="font-bold text-2xl mb-4 flex items-center gap-2 text-green-700">
                        <i class="fas fa-code text-green-500"></i> Implementation: Case-Based Reasoning
                    </h3>
                    <pre class="bg-gray-900 text-gray-100 rounded-xl p-6 overflow-x-auto text-sm font-mono shadow-inner border border-gray-700"><code># Python Pseudocode for Case-Based Reasoning in an Expert System

# 1. Store past cases (problems and solutions)
past_cases = [
    {"symptoms": ["fever", "cough"], "diagnosis": "flu"},
    {"symptoms": ["cough"], "diagnosis": "cold"}
]

# 2. LLM agent extracts current symptoms from user input
current_symptoms = llm_agent.extract_facts(user_input)

# 3. Find the most similar past case
best_match = find_best_match(past_cases, current_symptoms)

# 4. Suggest diagnosis from the best matching case
if best_match:
    print(f"Suggested diagnosis: {best_match['diagnosis']}")
else:
    print("No similar case found.")
</code></pre>
                </div>
            </div>
        </section>
    </div>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-analytics-compat.js"></script>
    <div id="footer-placeholder"></div>
    <script type="module" src="../js/common-components.js"></script>
    <script type="module" src="../js/app.js"></script>
</body>
</html>
