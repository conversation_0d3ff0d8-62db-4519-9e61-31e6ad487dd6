<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog - Agentic Architecture</title>
    <!-- Tailwind CSS CDN for JIT mode with plugins -->
    <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts - Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <!-- Blog Interactions CSS -->
    <link rel="stylesheet" href="../../css/blog-interactions.css">
</head>
<body class="bg-gray-100 text-gray-800 antialiased">

    
    <div id="header-placeholder"></div>

    <header class="bg-gradient-to-r from-purple-700 to-blue-800 text-white py-20 md:py-28 text-center">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="text-4xl md:text-5xl lg:text-6xl font-extrabold mb-4 leading-tight">
                Blog & Insights
            </h1>
            <p class="text-lg md:text-xl lg:text-2xl opacity-90 max-w-2xl mx-auto">
                Deep dives into AI implementation, industry analysis, and thought leadership on autonomous agent systems.
            </p>
        </div>
    </header>

    <main class="max-w-6xl mx-auto px-4 py-12">
        <!-- Filter Section -->
        <div class="mb-8">
            <div class="flex flex-wrap justify-center gap-4">
                <button class="filter-btn active px-6 py-2 rounded-full font-medium transition-all duration-300 bg-blue-600 text-white hover:bg-blue-700" data-filter="all">
                    All Posts
                </button>
                <button class="filter-btn px-6 py-2 rounded-full font-medium transition-all duration-300 bg-gray-200 text-gray-700 hover:bg-gray-300" data-filter="blog">
                    Blog Posts
                </button>
                <button class="filter-btn px-6 py-2 rounded-full font-medium transition-all duration-300 bg-gray-200 text-gray-700 hover:bg-gray-300" data-filter="news">
                    Industry News
                </button>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="posts-container">

            <article class="bg-white rounded-2xl shadow-xl p-6 md:p-8 flex flex-col hover:shadow-2xl transition duration-300 post-item" data-category="blog">
                <div class="flex justify-between items-center mb-4">
                    <span class="text-sm text-gray-500">July 20, 2025</span>
                    <span class="text-xs font-medium bg-green-100 text-green-800 px-2 py-1 rounded-full">BLOG POST</span>
                </div>

                <div class="w-full aspect-video overflow-hidden rounded-xl mb-6">
                    <a href="utcp-dns-agentic-world.html" class="block h-full">
                        <!-- Image would go here -->
                    </a>
                </div>

                <div class="flex flex-col flex-grow">
                    <a href="utcp-dns-agentic-world.html">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2 hover:text-blue-600 transition-colors">UTCP and DNS in an Agentic World</h3>
                    </a>
                    <p class="text-gray-700 leading-relaxed mb-4 flex-grow">
                        Exploring the future of network protocols in the age of autonomous agents. How UTCP and DNS are evolving to meet the demands of agentic systems.
                    </p>
                    <div class="flex flex-wrap gap-2 text-sm">
                        <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full font-medium">#Cloud</span>
                        <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full font-medium">#Architecture</span>
                        <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full font-medium">#Scalability</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-500 mt-4">
                        <span class="mr-4"><i class="fas fa-clock mr-1"></i> 6 min read</span>
                        <span class="post-stats" data-post-id="utcp-dns-agentic-world">
                            <i class="fas fa-heart mr-1"></i> <span class="like-count-display">0</span>
                        </span>
                    </div>
                </div>

                <div class="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
                    <div class="flex items-center gap-4">
                        <button class="like-btn flex items-center text-gray-500 hover:text-blue-600 transition-colors" data-post-id="utcp-dns-agentic-world">
                            <i class="far fa-heart mr-2 text-lg"></i> Like
                        </button>
                    </div>
                    <a href="utcp-dns-agentic-world.html" class="flex items-center text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                        Read More <i class="fas fa-arrow-right ml-2 text-sm"></i>
                    </a>
                </div>
            </article>

            <article class="bg-white rounded-2xl shadow-xl p-6 md:p-8 flex flex-col hover:shadow-2xl transition duration-300 post-item" data-category="blog">
                <div class="flex justify-between items-center mb-4">
                    <span class="text-sm text-gray-500">August 1, 2025</span>
                    <span class="text-xs font-medium bg-green-100 text-green-800 px-2 py-1 rounded-full">BLOG POST</span>
                </div>

                <div class="w-full aspect-video overflow-hidden rounded-xl mb-6">
                    <a href="kilo-code-ai-cost-optimization.html" class="block h-full">
                        <!-- Image would go here -->
                    </a>
                </div>

                <div class="flex flex-col flex-grow">
                    <a href="kilo-code-ai-cost-optimization.html">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2 hover:text-blue-600 transition-colors">Kilo Code AI: Optimizing Costs in AI Development</h3>
                    </a>
                    <p class="text-gray-700 leading-relaxed mb-4 flex-grow">
                        A deep dive into how Kilo Code AI is revolutionizing cost optimization in AI development, making advanced AI more accessible and affordable.
                    </p>
                    <div class="flex flex-wrap gap-2 text-sm">
                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">#AI</span>
                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">#Optimization</span>
                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">#Development</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-500 mt-4">
                        <span class="mr-4"><i class="fas fa-clock mr-1"></i> 8 min read</span>
                        <span class="post-stats" data-post-id="kilo-code-ai-cost-optimization">
                            <i class="fas fa-heart mr-1"></i> <span class="like-count-display">0</span>
                        </span>
                    </div>
                </div>

                <div class="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
                    <div class="flex items-center gap-4">
                        <button class="like-btn flex items-center text-gray-500 hover:text-blue-600 transition-colors" data-post-id="kilo-code-ai-cost-optimization">
                            <i class="far fa-heart mr-2 text-lg"></i> Like
                        </button>
                    </div>
                    <a href="kilo-code-ai-cost-optimization.html" class="flex items-center text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                        Read More <i class="fas fa-arrow-right ml-2 text-sm"></i>
                    </a>
                </div>
            </article>

            <article class="bg-white rounded-2xl shadow-xl p-6 md:p-8 flex flex-col hover:shadow-2xl transition duration-300 post-item" data-category="blog">
                <div class="flex justify-between items-center mb-4">
                    <span class="text-sm text-gray-500">August 15, 2025</span>
                    <span class="text-xs font-medium bg-green-100 text-green-800 px-2 py-1 rounded-full">BLOG POST</span>
                </div>

                <div class="w-full aspect-video overflow-hidden rounded-xl mb-6">
                    <a href="../agentic-workflow-patterns.html" class="block h-full">
                        <!-- Image would go here -->
                    </a>
                </div>

                <div class="flex flex-col flex-grow">
                    <a href="../agentic-workflow-patterns.html">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2 hover:text-blue-600 transition-colors">From Prompt Hacks to AI Powerhouses: 5 Patterns to Build Smarter AI</h3>
                    </a>
                    <p class="text-gray-700 leading-relaxed mb-4 flex-grow">
                        Discover 5 powerful agentic workflow patterns to transform your AI from simple prompts to robust, scalable systems.
                    </p>
                    <div class="flex flex-wrap gap-2 text-sm">
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">#AI</span>
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">#Patterns</span>
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">#Workflow</span>
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">#Architecture</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-500 mt-4">
                        <span class="mr-4"><i class="fas fa-clock mr-1"></i> 8 min read</span>
                        <span class="post-stats" data-post-id="ai-workflow-patterns">
                            <i class="fas fa-heart mr-1"></i> <span class="like-count-display">0</span>
                        </span>
                    </div>
                </div>

                <div class="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
                    <div class="flex items-center gap-4">
                        <button class="like-btn flex items-center text-gray-500 hover:text-blue-600 transition-colors" data-post-id="ai-workflow-patterns">
                            <i class="far fa-heart mr-2 text-lg"></i> Like
                        </button>
                    </div>
                    <a href="../agentic-workflow-patterns.html" class="flex items-center text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                        Read More <i class="fas fa-arrow-right ml-2 text-sm"></i>
                    </a>
                </div>
            </article>

            <!-- News Articles -->
            <article class="bg-white rounded-2xl shadow-xl p-6 md:p-8 flex flex-col hover:shadow-2xl transition duration-300 post-item" data-category="news">
                <div class="flex justify-between items-center mb-4">
                    <span class="text-sm text-gray-500">August 6, 2025</span>
                    <span class="text-xs font-medium bg-blue-100 text-blue-800 px-2 py-1 rounded-full">AI NEWS</span>
                </div>

                <div class="w-full aspect-video overflow-hidden rounded-xl mb-6">
                    <a href="openai-bold-open-source-pivot-aug-2025.html" class="block h-full">
                        <!-- Image would go here -->
                    </a>
                </div>

                <div class="flex flex-col flex-grow">
                    <a href="openai-bold-open-source-pivot-aug-2025.html">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2 hover:text-blue-600 transition-colors">OpenAI's Bold Open-Source Pivot: New Models Released, Ecosystem Rallies</h3>
                    </a>
                    <p class="text-gray-700 leading-relaxed mb-4 flex-grow">
                        In a game-changing announcement, OpenAI is open-sourcing two key AI systems behind ChatGPT, sharing technology freely with researchers and businesses. The move could accelerate innovation but raises questions around safety, governance, and competition.
                    </p>
                    <div class="flex flex-wrap gap-2 text-sm">
                        <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full font-medium">#OpenAI</span>
                        <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full font-medium">#Open Source</span>
                        <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full font-medium">#AI News</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-500 mt-4">
                        <span class="mr-4"><i class="fas fa-clock mr-1"></i> 4 min read</span>
                    </div>
                </div>

                <div class="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
                    <div class="flex items-center gap-4">
                        <span class="text-gray-500 text-sm">Industry Analysis</span>
                    </div>
                    <a href="openai-bold-open-source-pivot-aug-2025.html" class="flex items-center text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                        Read More <i class="fas fa-arrow-right ml-2 text-sm"></i>
                    </a>
                </div>
            </article>

            <article class="bg-white rounded-2xl shadow-xl p-6 md:p-8 flex flex-col hover:shadow-2xl transition duration-300 post-item" data-category="news">
                <div class="flex justify-between items-center mb-4">
                    <span class="text-sm text-gray-500">August 4, 2025</span>
                    <span class="text-xs font-medium bg-blue-100 text-blue-800 px-2 py-1 rounded-full">AI NEWS</span>
                </div>

                <div class="w-full aspect-video overflow-hidden rounded-xl mb-6">
                    <a href="china-ai-leadership-july-2025.html" class="block h-full">
                        <!-- Image would go here -->
                    </a>
                </div>

                <div class="flex flex-col flex-grow">
                    <a href="china-ai-leadership-july-2025.html">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2 hover:text-blue-600 transition-colors">China's AI Leadership: Open-Source Models Surge in July 2025</h3>
                    </a>
                    <p class="text-gray-700 leading-relaxed mb-4 flex-grow">
                        A look at the latest open-source AI models from China and their implications for global AI leadership, including GLM-4.5, Qwen3 Coder, and Kimi K2.
                    </p>
                    <div class="flex flex-wrap gap-2 text-sm">
                        <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full font-medium">#China AI</span>
                        <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full font-medium">#Open Source</span>
                        <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full font-medium">#Global AI</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-500 mt-4">
                        <span class="mr-4"><i class="fas fa-clock mr-1"></i> 5 min read</span>
                    </div>
                </div>

                <div class="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
                    <div class="flex items-center gap-4">
                        <span class="text-gray-500 text-sm">Industry Analysis</span>
                    </div>
                    <a href="china-ai-leadership-july-2025.html" class="flex items-center text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                        Read More <i class="fas fa-arrow-right ml-2 text-sm"></i>
                    </a>
                </div>
            </article>
        </div>
    </main>

    <!-- Footer Placeholder - common-components.js will inject content here -->
    <div id="footer-placeholder"></div>

    <!-- Firebase SDK -->
    <script type="importmap">
    {
        "imports": {
            "firebase/app": "https://www.gstatic.com/firebasejs/10.12.2/firebase-app.js",
            "firebase/analytics": "https://www.gstatic.com/firebasejs/10.12.2/firebase-analytics.js",
            "firebase/firestore": "https://www.gstatic.com/firebasejs/10.12.2/firebase-firestore.js",
            "firebase/auth": "https://www.gstatic.com/firebasejs/10.12.2/firebase-auth.js"
        }
    }
    </script>
    
    <!-- Application Scripts -->
    <script type="module" src="../../js/common-components.js"></script>
    <script type="module" src="../../js/blog-interactions.js"></script>
    <script type="module" src="../../js/blog-index.js"></script>

    <!-- Filter functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            const postItems = document.querySelectorAll('.post-item');

            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const filter = this.getAttribute('data-filter');

                    // Update active button
                    filterButtons.forEach(btn => {
                        btn.classList.remove('active', 'bg-blue-600', 'text-white');
                        btn.classList.add('bg-gray-200', 'text-gray-700');
                    });
                    this.classList.add('active', 'bg-blue-600', 'text-white');
                    this.classList.remove('bg-gray-200', 'text-gray-700');

                    // Filter posts
                    postItems.forEach(post => {
                        if (filter === 'all' || post.getAttribute('data-category') === filter) {
                            post.style.display = 'flex';
                            post.style.animation = 'fadeIn 0.3s ease-in';
                        } else {
                            post.style.display = 'none';
                        }
                    });
                });
            });
        });
    </script>

    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .filter-btn.active {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
    </style>
</body>
</html>
