<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog - Agentic Architecture</title>
    <!-- Tailwind CSS CDN for JIT mode with plugins -->
    <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts - Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <!-- Blog Interactions CSS -->
    <link rel="stylesheet" href="../../css/blog-interactions.css">
</head>
<body class="bg-gray-100 text-gray-800 antialiased">

    
    <div id="header-placeholder"></div>

    <header class="bg-gradient-to-r from-purple-700 to-blue-800 text-white py-20 md:py-28 text-center">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="text-4xl md:text-5xl lg:text-6xl font-extrabold mb-4 leading-tight">
                Agentic Architecture Blog
            </h1>
            <p class="text-lg md:text-xl lg:text-2xl opacity-90 max-w-2xl mx-auto">
                Insights, updates, and thought leadership on autonomous agent systems and their applications.
            </p>
        </div>
    </header>

    <main class="max-w-6xl mx-auto px-4 py-12">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">

            <article class="bg-white rounded-2xl shadow-xl p-6 md:p-8 flex flex-col hover:shadow-2xl transition duration-300">
                <div class="flex justify-between items-center mb-4">
                    <span class="text-sm text-gray-500">July 20, 2025</span>
                    <button class="text-gray-400 hover:text-gray-600 focus:outline-none">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
                
                <div class="w-full aspect-video overflow-hidden rounded-xl mb-6">
                    <a href="utcp-dns-agentic-world.html" class="block h-full">
                        <!-- Image would go here -->
                    </a>
                </div>

                <div class="flex flex-col flex-grow">
                    <a href="utcp-dns-agentic-world.html">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2 hover:text-blue-600 transition-colors">UTCP and DNS in an Agentic World</h3>
                    </a>
                    <p class="text-gray-700 leading-relaxed mb-4 flex-grow">
                        Exploring the future of network protocols in the age of autonomous agents. How UTCP and DNS are evolving to meet the demands of agentic systems.
                    </p>
                    <div class="flex flex-wrap gap-2 text-sm">
                        <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full font-medium">#Cloud</span>
                        <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full font-medium">#Architecture</span>
                        <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full font-medium">#Scalability</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-500 mt-4">
                        <span class="mr-4"><i class="fas fa-clock mr-1"></i> 6 min read</span>
                        <span class="post-stats" data-post-id="utcp-dns-agentic-world">
                            <i class="fas fa-heart mr-1"></i> <span class="like-count-display">0</span>
                        </span>
                    </div>
                </div>

                <div class="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
                    <div class="flex items-center gap-4">
                        <button class="like-btn flex items-center text-gray-500 hover:text-blue-600 transition-colors" data-post-id="utcp-dns-agentic-world">
                            <i class="far fa-heart mr-2 text-lg"></i> Like
                        </button>
                    </div>
                    <a href="utcp-dns-agentic-world.html" class="flex items-center text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                        Read More <i class="fas fa-arrow-right ml-2 text-sm"></i>
                    </a>
                </div>
            </article>

            <article class="bg-white rounded-2xl shadow-xl p-6 md:p-8 flex flex-col hover:shadow-2xl transition duration-300">
                <div class="flex justify-between items-center mb-4">
                    <span class="text-sm text-gray-500">August 1, 2025</span>
                    <button class="text-gray-400 hover:text-gray-600 focus:outline-none">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
                
                <div class="w-full aspect-video overflow-hidden rounded-xl mb-6">
                    <a href="kilo-code-ai-cost-optimization.html" class="block h-full">
                        <!-- Image would go here -->
                    </a>
                </div>

                <div class="flex flex-col flex-grow">
                    <a href="kilo-code-ai-cost-optimization.html">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2 hover:text-blue-600 transition-colors">Kilo Code AI: Optimizing Costs in AI Development</h3>
                    </a>
                    <p class="text-gray-700 leading-relaxed mb-4 flex-grow">
                        A deep dive into how Kilo Code AI is revolutionizing cost optimization in AI development, making advanced AI more accessible and affordable.
                    </p>
                    <div class="flex flex-wrap gap-2 text-sm">
                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">#AI</span>
                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">#Optimization</span>
                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">#Development</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-500 mt-4">
                        <span class="mr-4"><i class="fas fa-clock mr-1"></i> 8 min read</span>
                        <span class="post-stats" data-post-id="kilo-code-ai-cost-optimization">
                            <i class="fas fa-heart mr-1"></i> <span class="like-count-display">0</span>
                        </span>
                    </div>
                </div>

                <div class="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
                    <div class="flex items-center gap-4">
                        <button class="like-btn flex items-center text-gray-500 hover:text-blue-600 transition-colors" data-post-id="kilo-code-ai-cost-optimization">
                            <i class="far fa-heart mr-2 text-lg"></i> Like
                        </button>
                    </div>
                    <a href="kilo-code-ai-cost-optimization.html" class="flex items-center text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                        Read More <i class="fas fa-arrow-right ml-2 text-sm"></i>
                    </a>
                </div>
            </article>

            <article class="bg-white rounded-2xl shadow-xl p-6 md:p-8 flex flex-col hover:shadow-2xl transition duration-300">
                <div class="flex justify-between items-center mb-4">
                    <span class="text-sm text-gray-500">August 15, 2025</span>
                    <button class="text-gray-400 hover:text-gray-600 focus:outline-none">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
                
                <div class="w-full aspect-video overflow-hidden rounded-xl mb-6">
                    <a href="../agentic-workflow-patterns.html" class="block h-full">
                        <!-- Image would go here -->
                    </a>
                </div>

                <div class="flex flex-col flex-grow">
                    <a href="../agentic-workflow-patterns.html">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2 hover:text-blue-600 transition-colors">From Prompt Hacks to AI Powerhouses: 5 Patterns to Build Smarter AI</h3>
                    </a>
                    <p class="text-gray-700 leading-relaxed mb-4 flex-grow">
                        Discover 5 powerful agentic workflow patterns to transform your AI from simple prompts to robust, scalable systems.
                    </p>
                    <div class="flex flex-wrap gap-2 text-sm">
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">#AI</span>
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">#Patterns</span>
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">#Workflow</span>
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">#Architecture</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-500 mt-4">
                        <span class="mr-4"><i class="fas fa-clock mr-1"></i> 8 min read</span>
                        <span class="post-stats" data-post-id="ai-workflow-patterns">
                            <i class="fas fa-heart mr-1"></i> <span class="like-count-display">0</span>
                        </span>
                    </div>
                </div>

                <div class="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
                    <div class="flex items-center gap-4">
                        <button class="like-btn flex items-center text-gray-500 hover:text-blue-600 transition-colors" data-post-id="ai-workflow-patterns">
                            <i class="far fa-heart mr-2 text-lg"></i> Like
                        </button>
                    </div>
                    <a href="../agentic-workflow-patterns.html" class="flex items-center text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                        Read More <i class="fas fa-arrow-right ml-2 text-sm"></i>
                    </a>
                </div>
            </article>

            <!-- Add more news articles here following the same structure -->
        </div>
    </main>

    <!-- Footer Placeholder - common-components.js will inject content here -->
    <div id="footer-placeholder"></div>

    <!-- Firebase SDK -->
    <script type="importmap">
    {
        "imports": {
            "firebase/app": "https://www.gstatic.com/firebasejs/10.12.2/firebase-app.js",
            "firebase/analytics": "https://www.gstatic.com/firebasejs/10.12.2/firebase-analytics.js",
            "firebase/firestore": "https://www.gstatic.com/firebasejs/10.12.2/firebase-firestore.js",
            "firebase/auth": "https://www.gstatic.com/firebasejs/10.12.2/firebase-auth.js"
        }
    }
    </script>
    
    <!-- Application Scripts -->
    <script type="module" src="../../js/common-components.js"></script>
    <script type="module" src="../../js/blog-interactions.js"></script>
    <script type="module" src="../../js/blog-index.js"></script>
</body>
</html>
